import React, { useState, useRef, useEffect, useContext, useMemo, useCallback, useReducer } from "react";
import { useParams, useSearchParams } from "next/navigation";
import {
  Loader2,
  Maximize,
  RotateCw,
  Link2,
  X,
  ExternalLink,
  Minimize,
  Plus,
  BookOpen,
  Download,
  Rocket,
  RefreshCw
} from "lucide-react";
import CodeDiscussionPanel from "@/components/CodeDiscussionPanel";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useDeployment } from "@/components/Context/DeploymentContext";
import CodeViewPanel from "@/components/CodeGenrationPanel/CodeViewPanel/CodeViewPanel";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import {
  stopBatchTask,
} from "@/utils/api";
import { updateTask } from "@/utils/batchAPI";
import { usePlanRestriction } from "@/components/Context/PlanRestrictionContext";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import GitSidebar from "./GitInterface";
import TaskProgress from "./TaskProgress";
import useFullscreenHandler from "./useFullscreenHandler";
import DeploymentInterface from "./DeploymentInterface";
import { listDeployments, deleteDeployment } from "@/utils/deploymentApi";
import DocumentsRenderPanel from "@/components/CodeGenrationPanel/DocumentsPanel/DocumentsRenderPanel";

import Cookies from "js-cookie";
import ModelSelector from "@/components/ModelSelector/ModelSelector";
import { getPreviewUrl as getPreviewUrlFromBackend } from "@/utils/url_helpers";
import PreviewPanel from "@/components/CodeGenrationPanel/PreviewPanel/PreviewPanel";
import PreviewContainers from "@/components/CodeGenrationPanel/PreviewPanel/PreviewContainers";
import InactivityTimerModal from "./InactivityModal";
import { getLatestSubscription } from "@/utils/paymentAPI";
import { decryptToken } from "@/utils/auth";
import CreditBadge from "@/components/ui/CreditBadge"
import { mergeToKaviaMain } from "@/utils/batchAPI";

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url) => {
  if (!url || typeof url !== 'string') return url;
  // Replace any occurrence of :3000 with :4000 in the URL
  return url.replace(':3000', ':4000');
};

// Custom debounce utility to prevent cascading re-renders

const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Extract tab button rendering into memoized component
const TabButtons = React.memo(({
  currentTaskId,
  activeTab,
  toggleTab,
  documentContent,
  newDocAlert
}) => {
  const isDeepQuery = currentTaskId?.startsWith("deep-query-job");

  if (isDeepQuery) {
    return (
      <>
        <button
          role="tab"
          aria-selected={activeTab === "Editor"}
          className={`px-3 py-1 typography-caption font-weight-medium transition-all ${activeTab === "Editor"
            ? "bg-[#F26422] text-white"
            : "bg-white text-gray-700 hover:bg-gray-50"
            }`}
          onClick={() => toggleTab("Editor")}
        >
          Editor
        </button>
        {documentContent && (
          <button
            role="tab"
            aria-selected={activeTab === "Document"}
            className={`relative flex items-center gap-2 px-3 py-1 typography-caption font-weight-medium transition-all ${activeTab === "Document"
              ? "bg-[#F26422] text-white"
              : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            onClick={() => toggleTab("Document")}
          >
            {activeTab === "Editor" && newDocAlert && (
              <div className="relative pb-0.5">
                <div className="absolute inset-0 w-2 h-2 bg-red-500 rounded-full animate-ping opacity-75"></div>
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              </div>
            )}
            Documents
          </button>
        )}
      </>
    );
  }

  return (
    <>
      <button
        role="tab"
        aria-selected={activeTab === "Code"}
        className={`px-3 py-1 typography-caption font-weight-medium transition-all ${activeTab === "Code"
          ? "bg-[#F26422] text-white"
          : "bg-white text-gray-700 hover:bg-gray-50"
          }`}
        onClick={() => toggleTab("Code")}
      >
        Code
      </button>
      {documentContent && (
        <button
          role="tab"
          aria-selected={activeTab === "Document"}
          className={`relative flex items-center gap-2 px-3 py-1 typography-caption font-weight-medium transition-all ${activeTab === "Document"
            ? "bg-[#F26422] text-white"
            : "bg-white text-gray-700 hover:bg-gray-50"
            }`}
          onClick={() => toggleTab("Document")}
        >
          {(activeTab === "Preview" || activeTab === "Code") && newDocAlert && (
            <div className="relative pb-0.5">
              <div className="absolute inset-0 w-2 h-2 bg-red-500 rounded-full animate-ping opacity-75"></div>
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            </div>
          )}
          Documents
        </button>
      )}
      <button
        role="tab"
        aria-selected={activeTab === "Preview"}
        className={`px-3 py-1 typography-caption font-weight-medium transition-all ${activeTab === "Preview"
          ? "bg-[#F26422] text-white"
          : "bg-white text-gray-700 hover:bg-gray-50"
          }`}
        onClick={() => toggleTab("Preview")}
      >
        Preview
      </button>
    </>
  );
});

TabButtons.displayName = 'TabButtons';

// Extract tab content into memoized component
const TabContent = React.memo(({
  currentTaskId,
  activeTab,
  documentContent,
  isSwaggerViewActive,
  formatWorkspaceUrl,
  currentIp,
  isPreviewLoading,
  previewError,
  handlePreviewRefresh,
  handlePortChange,
  portNumber,
  iframeKey,
  previewUrl,
  currentUrl,
  activeView,
  isLoading,
  setIsLoading,
  projectId
}) => {
  const isDeepQuery = currentTaskId?.startsWith("deep-query-job");

  if (isDeepQuery) {
    return (
      <div className="w-full h-full overflow-hidden">
        {documentContent && (
          <div
            className="p-2 w-full h-full"
            style={{
              display: activeTab === "Document" ? "block" : "none",
            }}
          >
            <DocumentsRenderPanel activeTab={activeTab} projectId={projectId} taskId={currentTaskId}/>
          </div>
        )}
        <div
          className="p-2 w-full h-full"
          style={{
            display: activeTab === "Editor" ? "block" : "none",
          }}
        >
          <CodeViewPanel />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-hidden">
      <div
        className="p-2 w-full h-full"
        style={{
          display: activeTab === "Code" ? "block" : "none",
        }}
      >
        <CodeViewPanel />
      </div>
      <div
        className="p-2 w-full h-full"
        style={{
          display: activeTab === "Document" ? "block" : "none",
        }}
      >
        <DocumentsRenderPanel projectId={projectId} taskId={currentTaskId}/>
      </div>
      {activeTab === "Preview" && (
        <div className="p-2 w-full h-full">
          <PreviewPanel
            currentTaskId={currentTaskId}
            currentIp={currentIp}
            formatWorkspaceUrl={formatWorkspaceUrl}
            portNumber={portNumber}
            onPortChange={handlePortChange}
          />
        </div>
      )}
    </div>
  );
});

TabContent.displayName = 'TabContent';

// Extract URL display section
const UrlDisplaySection = React.memo(({
  currentTaskId,
  activeTab,
  showUrl,
  handleRefresh,
  handleCopy,
  copied
}) => {
  const shouldShowUrl = (!currentTaskId?.startsWith("deep-query-job") || activeTab === "Editor");

  if (!shouldShowUrl) return null;

  if (activeTab === "Preview") {
    return null
  }

  return (
    <div className="flex items-center bg-gray-100 rounded-md px-2 py-1 min-w-[196px] max-w-[35%] justify-between shadow-sm">
      <BootstrapTooltip title="Refresh" placement="bottom">
        <button
          className="p-0.5 text-gray-500 hover:text-gray-700"
          onClick={handleRefresh}
        >
          <RotateCw className="h-3 w-3" />
        </button>
      </BootstrapTooltip>
      <span className="text-gray-400 typography-caption mx-2 truncate">
        {showUrl}
      </span>
      <BootstrapTooltip
        title={copied ? "Copied!" : "Copy URL"}
        placement="bottom"
      >
        <button
          className={`p-0.5 ${copied
            ? "text-green-600 hover:bg-green-50"
            : "text-gray-500 hover:bg-gray-100 hover:text-gray-700"
            }`}
          onClick={handleCopy}
        >
          <Link2 className="h-3 w-3" />
        </button>
      </BootstrapTooltip>
    </div>
  );
});

UrlDisplaySection.displayName = 'UrlDisplaySection';

// Extract action buttons section
const TabActionButtons = React.memo(({
  currentTaskId,
  activeTab,
  showUrl,
  activeView,
  setActiveView,
  isSwaggerViewActive,
  toggleSwaggerView,
  isFullscreen,
  toggleFullscreen
}) => {
  const shouldShowUrlButtons = (!currentTaskId?.startsWith("deep-query-job") ||
    activeTab === "Code" ||
    activeTab === "Editor");

  return (
    <div className="flex items-center">

      {(shouldShowUrlButtons && (activeTab !== "Preview")) && (
        <>
          <button
            className="p-0.5 text-gray-500 hover:text-gray-700"
            onClick={() => window.open(showUrl, "_blank")}
          >
            <svg
              width="20px"
              height="20px"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.5 17C4.0875 17 3.73437 16.8541 3.44062 16.5604C3.14687 16.2666 3 15.9135 3 15.501V4.501C3 4.0885 3.14687 3.73537 3.44062 3.44162C3.73437 3.14787 4.0875 3.001 4.5 3.001H10V4.501H4.5V15.501H15.5V10.001H17V15.501C17 15.9135 16.8531 16.2666 16.5594 16.5604C16.2656 16.8541 15.9125 17 15.5 17H4.5ZM8.0625 13.001L7 11.9385L14.4375 4.501H12V3.001H17V8.001H15.5V5.5635L8.0625 13.001Z"
                fill="#4B5563"
              />
            </svg>
          </button>
          {activeTab === "Preview" && (
            <>
              <button
                className="p-0.5 text-gray-500 hover:text-gray-700 ml-1"
                onClick={() =>
                  setActiveView(
                    activeView === "console"
                      ? "browser"
                      : "console"
                  )
                }
              >
                <svg
                  width="20px"
                  height="20px"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2 16V14H4V5.5C4 5.0875 4.14687 4.73438 4.44062 4.44063C4.73438 4.14688 5.0875 4 5.5 4H17V5.5H5.5V14H10V16H2ZM12.7558 16C12.5436 16 12.3646 15.9282 12.2188 15.7846C12.0729 15.641 12 15.463 12 15.2506V7.75604C12 7.54368 12.0718 7.36458 12.2154 7.21875C12.359 7.07292 12.5369 7 12.7492 7H17.2442C17.4564 7 17.6354 7.07181 17.7812 7.21542C17.9271 7.35903 18 7.53701 18 7.74937V15.244C18 15.4563 17.9282 15.6354 17.7846 15.7813C17.641 15.9271 17.4631 16 17.2508 16H12.7558ZM13.5 14H16.5V8.5H13.5V14Z"
                    fill="#4B5563"
                  />
                </svg>
              </button>
              <BootstrapTooltip
                title="Swagger View"
                placement="bottom"
              >
                <button
                  className={`p-0.5 ml-1 ${isSwaggerViewActive
                    ? "text-primary"
                    : "text-gray-500 hover:text-gray-700"
                    }`}
                  onClick={toggleSwaggerView}
                >
                  <BookOpen size={20} />
                </button>
              </BootstrapTooltip>
            </>
          )}
        </>
      )}
      <BootstrapTooltip
        title={
          isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"
        }
        placement="bottom"
      >
        <button
          className="p-0.5 text-gray-500 hover:text-gray-700 ml-1"
          onClick={toggleFullscreen}
        >
          {isFullscreen ? (
            <Minimize size={20} />
          ) : (
            <Maximize size={20} />
          )}
        </button>
      </BootstrapTooltip>
    </div>
  );
});

TabActionButtons.displayName = 'TabActionButtons';

// Add state reducer for related UI states to prevent cascading re-renders
const uiStateReducer = (state, action) => {
  switch (action.type) {
    case 'SET_TAB_AND_URL':
      return {
        ...state,
        activeTab: action.activeTab,
        showUrl: action.showUrl,
      };
    case 'SET_PREVIEW_STATE':
      return {
        ...state,
        isPreviewLoading: action.isPreviewLoading,
        previewError: action.previewError,
        previewUrl: action.previewUrl,
      };
    case 'SET_URL_STATE':
      return {
        ...state,
        currentUrl: action.currentUrl,
        showUrl: action.showUrl,
      };
    case 'BATCH_UPDATE':
      return {
        ...state,
        ...action.updates,
      };
    default:
      return state;
  }
};

const CodeGenerationModal = () => {
  const {
    isVisible,
    tab,
    isReady,
    closeModal,
    currentIframeUrl,
    notification,
    activeTab,
    setActiveTab,
    setSelectedFile,
    architectureId,
    sessionName,
    setSessionName,
    fetchCurrentUrl,
    currentIp,
    steps,
    wsStatus,
    formatWorkspaceUrl,
    wsConnection,
    setStatusData,
    newDocAlert,
    llmModel,
    setLlmModel,
    activeView,
    setActiveView,
    documentContent,
    taskDetails,
    timeoutWarning,
    refreshCodeEditor,
    containers,
    selectedContainer,
  } = useCodeGeneration();

  const { showPlanRestriction, creditLimitCrossed } = usePlanRestriction();

  // Use reducer for related UI states to prevent cascading re-renders
  const [uiState, dispatchUiState] = useReducer(uiStateReducer, {
    currentUrl: "",
    showUrl: null,
    isPreviewLoading: true,
    previewError: null,
    previewUrl: null,
  });

  // Individual states that don't cascade
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [iframeKey, setIframeKey] = useState(Date.now());
  const [portNumber, setPortNumber] = useState(3000);
  const [isCodeInitialized, setIsCodeInitialized] = useState(true);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isCodeViewLoaded, setCodeViewLoaded] = useState(false);
  const [isPanelExpanded, setIsPanelExpanded] = useState(false);
  const { projectId } = useParams();
  const searchParams = useSearchParams();
  const currentTaskId = searchParams.get("task_id");
  const { showAlert } = useContext(AlertContext);
  const [isRefreshingPlan, setIsRefreshingPlan] = useState(false);

  // Add minimal status tracking
  const [currentTaskStatus, setCurrentTaskStatus] = useState(null);


  const [isUpdating, setIsUpdating] = useState(false);
  const [isTerminating, setIsTerminating] = useState(false);
  const [isMerging, setIsMerging] = useState(false);
  const isFirstRender = useRef(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { isFullscreen, toggleFullscreen, rightPanelRef } = useFullscreenHandler();
  const [isTaskProgressOpen, setIsTaskProgressOpen] = useState(false);
  const deployPanelRef = useRef(null);
  const [showDeploymentsModal, setShowDeploymentsModal] = useState(false);
  const [deployments, setDeployments] = useState([]);
  const [isLoadingDeployments, setIsLoadingDeployments] = useState(false);

  // Add container details panel state
  const [showContainerDetailsPanel, setShowContainerDetailsPanel] = useState(false);
  const [selectedContainerForDeployment, setSelectedContainerForDeployment] = useState(null);
  const [isLoadingManifest, setIsLoadingManifest] = useState(false);

  const [availableModels, setAvailableModels] = useState([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [isSwaggerViewActive, setIsSwaggerViewActive] = useState(false);
  const [terminateTimeoutId, setTerminateTimeoutId] = useState(null);
  const [isMergeAndTerminate, setIsMergeAndTerminate] = useState(false);
  const tenantId = Cookies.get('tenant_id');
  const [subscriptionData, setSubscriptionData] = useState({
    currentPlan: 'Loading...',
    planCredits: null,
    organizationCost: 0
  });
  // Initialize with sessionName if it exists, otherwise "Untitled"
  const [sessionInputValue, setSessionInputValue] = useState(sessionName || "Untitled");
  const [selectedOption, setSelectedOption] = useState("save");
  const [actionLoading, setActionLoading] = useState(false);

  // Reset selectedOption to "save" whenever modal opens
  useEffect(() => {
    if (showConfirmModal) {
      setSelectedOption("save");
    }
  }, [showConfirmModal]);

  // Use refs for values that don't need to trigger re-renders
  const currentUrlRef = useRef(uiState.currentUrl);
  const showUrlRef = useRef(uiState.showUrl);

  // Update refs when state changes (for accessing current values in callbacks)
  useEffect(() => {
    currentUrlRef.current = uiState.currentUrl;
    showUrlRef.current = uiState.showUrl;
  }, [uiState.currentUrl, uiState.showUrl]);

  // Add deployment context
  const {
    isDeploymentConfigOpen,
    setIsDeploymentConfigOpen,
    isDeployPanelOpen,
    setIsDeployPanelOpen,
    setWsConnection: setDeploymentWsConnection,
    handleDeployClick,
    showDeploymentSuccess,
    setShowDeploymentSuccess,
    deployedUrl,
    fetchRepositories,
    isDeploying,
    setIsDeploying,
    currentRepository,
    manifest,
    fetchManifest,
  } = useDeployment();

  // Add tasks state
  const [tasks, setTasks] = useState([]);

  // Memoize expensive computations to prevent unnecessary re-renders
  const hasValidUrl = useMemo(() => {
    return uiState.currentUrl &&
      uiState.currentUrl !== "No URL found" &&
      uiState.currentUrl !== "";
  }, [uiState.currentUrl]);

  const isDeepQuery = useMemo(() => {
    return currentTaskId?.startsWith("deep-query-job");
  }, [currentTaskId]);

  const isCodeMaintenance = useMemo(() => {
    return currentTaskId?.startsWith("cm");
  }, [currentTaskId]);

  // Memoize header generation to prevent re-creation on every render
  const getHeadersRaw = useCallback(() => {
    const idToken = Cookies.get("idToken");
    const tenant_id = Cookies.get("tenant_id");

    return {
      Authorization: `Bearer ${idToken}`,
      "Content-Type": "application/json",
      "X-Tenant-ID": tenant_id,
    };
  }, []);

  // Debounced URL updates to prevent cascading re-renders
  const debouncedUrlUpdate = useCallback(
    debounce((newUrl, shouldUpdateShowUrl = false) => {
      dispatchUiState({
        type: 'BATCH_UPDATE',
        updates: {
          currentUrl: newUrl,
          ...(shouldUpdateShowUrl && { showUrl: newUrl }),
        }
      });
    }, 300),
    []
  );

  // Removed subscription fetching - will be handled globally
  const fetchCurrentPlan = async () => {

    try {

      const idToken = Cookies.get("idToken");
      const userData = decryptToken(idToken);
      const userId = userData.sub

      if (tenantId && userId) {
        const subscription = await getLatestSubscription(userId);

        if (subscription && subscription.price_id) {
          const cleanedCost = subscription.current_cost
            ? parseFloat(subscription.current_cost.replace('$', '').trim())
            : 0;
          // Now we can directly use the fields from the enhanced endpoint response
          setSubscriptionData({
            currentPlan: subscription.product_name,
            planCredits: subscription.credits || 50000, //50000 is for free
            organizationCost: cleanedCost
          });
        }
        else {
          setSubscriptionData({
            currentPlan: 'Free',
            planCredits: 50000,
            organizationCost: 0
          });
        }
      }
    } catch (error) {

      setSubscriptionData({
        currentPlan: 'Free',
        planCredits: 50000,
        organizationCost: 0
      });
      // showAlert("There was an error loading your subscription data. Defaulting to Free plan.", "error");
    } finally {

    }
  };
  useEffect(() => {
    fetchCurrentPlan()
  }, [searchParams, currentTaskId, projectId, tenantId])

  const handleRefreshPlan = async () => {
    setIsRefreshingPlan(true);
    await fetchCurrentPlan();
    // Show loading for at least 1 second
    setTimeout(() => {
      setIsRefreshingPlan(false);
    }, 1000);
  };

  // Consolidated tab switching logic to prevent cascading re-renders
  const toggleTab = useCallback((label) => {
    setActiveTab(label);

    // Batch the tab and URL state update
    let newShowUrl = null;

    if (isDeepQuery) {
      if (label === "Editor") {
        newShowUrl = currentIframeUrl;
      }
    } else {
      if (label === "Code") {
        newShowUrl = currentIframeUrl;
      } else if (label === "Preview") {
        newShowUrl = uiState.previewUrl || uiState.currentUrl;
      }
    }

    // Batch update to prevent cascading re-renders
    dispatchUiState({
      type: 'SET_TAB_AND_URL',
      activeTab: label,
      showUrl: newShowUrl,
    });
  }, [isDeepQuery, currentIframeUrl, uiState.previewUrl, uiState.currentUrl, setActiveTab]);

  // Initialize tasks when steps change - memoized to prevent unnecessary re-renders
  const formattedTasks = useMemo(() => {
    if (!steps) return [];

    return steps.map((step, index) => ({
      id: index + 1,
      title: step.name || `Task ${index + 1}`,
      description: step.description || "",
      status: step.status || "pending",
    }));
  }, [steps]);

  useEffect(() => {
    setTasks(formattedTasks);
  }, [formattedTasks]);

  const openSidebar = useCallback(() => {
    setIsSidebarOpen(true);
  }, []);

  const closeSidebar = useCallback(() => {
    setIsSidebarOpen(false);
  }, []);

  // Add cleanup refs to track active connections and timers
  const activeTimersRef = useRef(new Set());
  const eventSourceRef = useRef(null);
  const isComponentMountedRef = useRef(true);

  // Enhanced cleanup utility function
  const addTimerToCleanup = useCallback((timerId) => {
    activeTimersRef.current.add(timerId);
  }, []);

  const clearAllActiveTimers = useCallback(() => {
    activeTimersRef.current.forEach(timerId => {
      clearTimeout(timerId);
      clearInterval(timerId);
    });
    activeTimersRef.current.clear();
  }, []);

  // Safe async operations utility
  const safeAsyncOperation = useCallback((asyncFn) => {
    return (...args) => {
      if (isComponentMountedRef.current) {
        return asyncFn(...args);
      }
    };
  }, []);

  // Handle merge operation
  const handleMerge = useCallback(async () => {
    if (!currentTaskId) {
      showAlert("Unable to execute merge command - no task ID", "error");
      setIsMerging(false);
      setIsMergeAndTerminate(false);
      return;
    }

    setActionLoading(true);
    setIsMerging(true);
    setIsMergeAndTerminate(true);

    try {
      // Call the merge API endpoint
      await mergeToKaviaMain(currentTaskId);
      
      // If merge was successful, send stop command via WebSocket
      if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
        wsConnection.send(JSON.stringify({
          type: "stop",
          task_id: currentTaskId,
        }));
      }
      
      // Update task name in background
      const finalSessionName = sessionInputValue.trim() || "Untitled";
      updateTask(currentTaskId, { session_name: finalSessionName })
        .then(() => {
          if (isComponentMountedRef.current) {
            setSessionName(finalSessionName);
          }
        })
        .catch((error) => {
          console.error("Background task update failed:", error);
        });

      // Reset deployment context states
      setIsDeploymentConfigOpen(false);
      setIsDeployPanelOpen(false);
      setIsDeploying(false);
      setShowDeploymentSuccess(false);
      setDeploymentWsConnection(null);

      // Reset loading states
      setActionLoading(false);
      setIsMerging(false);
      setIsMergeAndTerminate(false);
      setShowConfirmModal(false);
      
      // Close the modal
      closeModal();
      
      showAlert("Session saved and closed successfully", "success");

    } catch (error) {
      console.error("Merge operation error:", error);
      
      if (isComponentMountedRef.current) {
        setActionLoading(false);
        setIsMerging(false);
        setIsMergeAndTerminate(false);
        showAlert("Couldn't merge to kavia main. Try later.", "error");
      }
    }
  }, [currentTaskId, showAlert, sessionInputValue, setSessionName, setIsDeploymentConfigOpen, setIsDeployPanelOpen, setIsDeploying, setShowDeploymentSuccess, setDeploymentWsConnection, closeModal]);

  // Consolidated URL and preview management to prevent cascading updates
  useEffect(() => {
    if (!currentIp || !portNumber || !currentIframeUrl) return;

    const newCurrentUrl = getPreviewUrlFromBackend(currentIframeUrl, portNumber);
    const newPreviewUrl = getPreviewUrlFromBackend(currentIframeUrl, 3001);

    // Batch all URL-related updates
    dispatchUiState({
      type: 'BATCH_UPDATE',
      updates: {
        currentUrl: newCurrentUrl,
        previewUrl: newPreviewUrl,
        isPreviewLoading: false,
      }
    });
  }, [currentIframeUrl, currentIp, portNumber]);

  // Consolidated URL display logic - only update when necessary
  useEffect(() => {
    let newShowUrl = null;

    if (isDeepQuery) {
      if (activeTab === "Editor") {
        newShowUrl = currentIframeUrl;
      }
    } else {
      if (activeTab === "Code") {
        newShowUrl = currentIframeUrl;
      } else if (activeTab === "Preview") {
        newShowUrl = uiState.previewUrl || uiState.currentUrl;
      }
    }

    // Only update if the URL actually changed
    if (newShowUrl !== uiState.showUrl) {
      dispatchUiState({
        type: 'BATCH_UPDATE',
        updates: { showUrl: newShowUrl }
      });
    }
  }, [activeTab, currentIframeUrl, uiState.previewUrl, uiState.currentUrl, isDeepQuery, uiState.showUrl]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      isComponentMountedRef.current = false;
      clearAllActiveTimers();

      // Cleanup terminate timeout if exists
      if (terminateTimeoutId) {
        clearTimeout(terminateTimeoutId);
      }

      // Cleanup EventSource
      if (eventSourceRef.current) {
        eventSourceRef.current.abort();
        eventSourceRef.current = null;
      }
    };
  }, [clearAllActiveTimers, terminateTimeoutId]);

  // Removed EventSource connection - using WebSocket only
  useEffect(() => {
    if (!currentTaskId) return;

    if (isDeepQuery) {
      // Set Editor as the default active tab for deep analysis
      toggleTab("Editor");
    }
  }, [currentTaskId, isDeepQuery, toggleTab]);

  // Initial URL fetch and status initialization - only when modal becomes visible
  useEffect(() => {
    if (isVisible) {
      fetchCurrentUrl();
    }
  }, [isVisible, fetchCurrentUrl]);

  // Initialize status from taskDetails when available
  useEffect(() => {
    if (taskDetails?.status && !currentTaskStatus) {
      setCurrentTaskStatus(taskDetails.status);
    }
  }, [taskDetails?.status, currentTaskStatus]);

  // Removed URL status checking - not needed for basic functionality

  // Enhanced refresh handler with timer cleanup
  const handleRefresh = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    // Update iframe key to force re-render for preview
    setIframeKey(Date.now());

    // Also refresh the code editor
    if (refreshCodeEditor) {
      refreshCodeEditor();
    }
  }, [portNumber, refreshCodeEditor]);

  // Enhanced preview refresh handler with timer cleanup
  const handlePreviewRefresh = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    dispatchUiState({
      type: 'SET_PREVIEW_STATE',
      isPreviewLoading: true,
      previewError: null,
    });

    setIsSwaggerViewActive(false);
    setIframeKey(Date.now());

    // Simulate refresh delay with proper cleanup
    const timerId = setTimeout(() => {
      if (isComponentMountedRef.current) {
        dispatchUiState({
          type: 'BATCH_UPDATE',
          updates: { isPreviewLoading: false }
        });
      }
      activeTimersRef.current.delete(timerId);
    }, 1000);

    addTimerToCleanup(timerId);
  }, [addTimerToCleanup]);

  // Enhanced copy handler with timer cleanup
  const handleCopy = useCallback(async () => {
    try {
      if (!uiState.showUrl || !isComponentMountedRef.current) return;
      await navigator.clipboard.writeText(uiState.showUrl);

      if (isComponentMountedRef.current) {
        setCopied(true);
        const timerId = setTimeout(() => {
          if (isComponentMountedRef.current) {
            setCopied(false);
          }
          activeTimersRef.current.delete(timerId);
        }, 2000);
        addTimerToCleanup(timerId);
      }
    } catch (error) {
      if (isComponentMountedRef.current) {
        alert("Failed to copy URL");
      }
    }
  }, [uiState.showUrl, addTimerToCleanup]);

  // Add download logs function
  const handleDownloadLogs = useCallback(async () => {
    if (!currentTaskId || !projectId) {
      showAlert("Missing task or project information", "error");
      return;
    }

    const tenant_id = Cookies.get("tenant_id");
    if (!tenant_id) {
      showAlert("Tenant information not found", "error");
      return;
    }

    try {
      showAlert("Preparing logs download...", "info");

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/manage/super/download_logs_CGA/${tenant_id}/${projectId}/${currentTaskId}`,
        {
          method: 'GET',
          headers: getHeadersRaw(),
        }
      );

      // Handle different HTTP status codes properly
      if (!response.ok) {
        let errorMessage = "Failed to download logs";

        try {
          // Try to parse error response as JSON first
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } catch {
          // If JSON parsing fails, use response status text
          errorMessage = response.statusText || errorMessage;
        }

        // Handle specific status codes with user-friendly messages
        switch (response.status) {
          case 404:
            showAlert(`No logs found for task ${currentTaskId}`, "error");
            break;
          case 403:
            showAlert("You are not authorized to download logs for this tenant", "error");
            break;
          case 500:
            if (errorMessage.includes("No allowed tenants configured")) {
              showAlert("Log download feature is not configured. Contact administrator", "error");
            } else {
              showAlert(`Server error: ${errorMessage}`, "error");
            }
            break;
          case 401:
            showAlert("Authentication required. Please log in again", "error");
            break;
          case 429:
            showAlert("Too many requests. Please try again later", "error");
            break;
          default:
            showAlert(`Error (${response.status}): ${errorMessage}`, "error");
        }
        return;
      }

      // Process successful response
      const blob = await response.blob();

      // Validate blob size
      if (blob.size === 0) {
        showAlert("Downloaded file is empty. No logs available", "warning");
        return;
      }

      // Extract filename from Content-Disposition header if available
      let filename = `logs-${currentTaskId}-${new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]}.zip`;
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create and trigger download
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      downloadLink.style.display = 'none';
      downloadLink.href = url;
      downloadLink.download = filename;

      // Add to DOM, click, and cleanup
      document.body.appendChild(downloadLink);
      downloadLink.click();

      // Cleanup
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(downloadLink);
      }, 100);

      showAlert(`Logs downloaded successfully: ${filename}`, "success");

    } catch (error) {
      console.error("Download logs error:", error);

      // Handle network and other errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        showAlert("Network error. Please check your connection and try again", "error");
      } else if (error.name === 'AbortError') {
        showAlert("Download was cancelled", "warning");
      } else {
        showAlert(`Unexpected error: ${error.message}`, "error");
      }
    }
  }, [currentTaskId, projectId, getHeadersRaw, showAlert]);
  // Session name input management
  /*useEffect(() => {
    if (
      sessionName &&
      sessionName !== "Untitled" &&
      sessionName !== sessionInputValue
    ) {
      setSessionInputValue(sessionName);
    }
  }, [sessionName, sessionInputValue]);*/
  // Memoized port change handler
  const handlePortChange = useCallback((event) => {
    setPortNumber(Number(event.target.value));
  }, []);

  // Enhanced modal close handlers with proper cleanup
  const handleConfirmClose = useCallback(() => {
    // Close any open deployment interfaces first
    setIsDeploymentConfigOpen(false);
    setIsDeployPanelOpen(false);
    setShowDeploymentSuccess(false);
    setIsDeploying(false);

    // Set the default option before showing the modal
    setSelectedOption("save");
    setShowConfirmModal(true);
    
    if (sessionStorage.getItem("isCodeMaintenance")) {
      sessionStorage.removeItem("isCodeMaintenance");
      sessionStorage.removeItem("selectedRepos");
    }
  }, [setIsDeploymentConfigOpen, setIsDeployPanelOpen, setShowDeploymentSuccess, setIsDeploying]);

  const confirmCloseModal = useCallback(async () => {
    setActionLoading(true);
    setIsUpdating(true);

    // Set a timeout to ensure the action completes within 5 seconds
    const timeoutId = setTimeout(() => {
      if (isComponentMountedRef.current && actionLoading) {
        console.warn("Continue session timeout (5s) - automatically closing modal");

        // Force reset all loading states
        setActionLoading(false);
        setIsUpdating(false);
        setShowConfirmModal(false);
        closeModal();

        showAlert("Session closed - operation timed out", "warning");
      }
      activeTimersRef.current.delete(timeoutId);
    }, 5000); // 5 second timeout

    addTimerToCleanup(timeoutId);

    try {
      const finalSessionName = sessionInputValue.trim() || "Untitled";
      await updateTask(currentTaskId, { session_name: finalSessionName });

      // Clear the timeout since operation completed successfully
      clearTimeout(timeoutId);
      activeTimersRef.current.delete(timeoutId);

      if (isComponentMountedRef.current) {
        setSessionName(finalSessionName);

        // Reset deployment context states
        setIsDeploymentConfigOpen(false);
        setIsDeployPanelOpen(false);
        setIsDeploying(false);
        setShowDeploymentSuccess(false);
        setDeploymentWsConnection(null);

        setShowConfirmModal(false);
        closeModal();
      }
    } catch (error) {
      // Clear the timeout on error
      clearTimeout(timeoutId);
      activeTimersRef.current.delete(timeoutId);

      if (isComponentMountedRef.current) {
        showAlert("Failed to update session name", "error");
      }
    } finally {
      if (isComponentMountedRef.current) {
        setIsUpdating(false);
        setActionLoading(false);
      }
    }
  }, [sessionInputValue, currentTaskId, setSessionName, showAlert, setIsDeploymentConfigOpen, setIsDeployPanelOpen, setIsDeploying, addTimerToCleanup]);

  const handleStopAndClose = useCallback(async () => {
    setActionLoading(true);
    setIsTerminating(true);

    try {
      const finalSessionName = sessionInputValue.trim() || "Untitled";

      // Send stop message through websocket if connected
      if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
        wsConnection.send(JSON.stringify({
          type: "stop",
          task_id: currentTaskId,
        }));

        // Wait for 5 seconds after sending stop message, then close modal
        const stopDelayTimeoutId = setTimeout(async () => {
          if (isComponentMountedRef.current) {
            console.log("5 seconds elapsed after stop message - closing modal");
            try {
              // Update task name in background
              await updateTask(currentTaskId, { session_name: finalSessionName });
              
              if (isComponentMountedRef.current) {
                setSessionName(finalSessionName);
              }
            } catch (error) {
              console.error("Background task update failed:", error);
            }

            // Reset deployment context states
            setIsDeploymentConfigOpen(false);
            setIsDeployPanelOpen(false);
            setIsDeploying(false);
            setShowDeploymentSuccess(false);
            setDeploymentWsConnection(null);

            // Reset loading states
            setIsTerminating(false);
            setActionLoading(false);
            setShowConfirmModal(false);
            
            // Close the modal
            closeModal();
          }
          activeTimersRef.current.delete(stopDelayTimeoutId);
        }, 5000); // 5 second delay

        addTimerToCleanup(stopDelayTimeoutId);

        // Set a fallback timeout to API call if websocket doesn't respond within 10 seconds
        const timeoutId = setTimeout(async () => {
          if (isComponentMountedRef.current && isTerminating) {
            console.warn("Websocket stop timeout (10s) - fallback to API call");
            try {
              await Promise.all([
                updateTask(currentTaskId, { session_name: finalSessionName }),
                stopBatchTask({ taskId: currentTaskId, projectId }),
              ]);

              // Reset deployment context states
              setIsDeploymentConfigOpen(false);
              setIsDeployPanelOpen(false);
              setIsDeploying(false);
              setShowDeploymentSuccess(false);
              setDeploymentWsConnection(null);

              setIsTerminating(false);
              setActionLoading(false);
              setShowConfirmModal(false);
              closeModal();
            } catch (error) {
              if (isComponentMountedRef.current) {
                setIsTerminating(false);
                setActionLoading(false);
                showAlert("Failed to terminate session", "error");
              }
            }
          }
        }, 10000); // 10 second timeout for fallback

        setTerminateTimeoutId(timeoutId);
        addTimerToCleanup(timeoutId);

        // Don't proceed further here - wait for the 5-second delay
        return;
      }

      // Fallback to API call if websocket is not available
      await Promise.all([
        updateTask(currentTaskId, { session_name: finalSessionName }),
        stopBatchTask({ taskId: currentTaskId, projectId }),
      ]);

      // Reset deployment context states
      setIsDeploymentConfigOpen(false);
      setIsDeployPanelOpen(false);
      setIsDeploying(false);
      setShowDeploymentSuccess(false);
      setDeploymentWsConnection(null);

      if (isComponentMountedRef.current) {
        setShowConfirmModal(false);
        closeModal();
      }
    } catch (error) {
      if (isComponentMountedRef.current) {
        showAlert("Failed to process your request", "error");
        setIsTerminating(false);
        setActionLoading(false);
      }
    }
  }, [sessionInputValue, currentTaskId, projectId, wsConnection, showAlert, setIsDeploymentConfigOpen, setIsDeployPanelOpen, setIsDeploying, setShowDeploymentSuccess, setDeploymentWsConnection, closeModal, addTimerToCleanup, setSessionName]);

  // Consolidate file selection and tab effects
  useEffect(() => {
    if (activeTab !== "File Watch" && setSelectedFile) {
      setSelectedFile(null);
    }
  }, [activeTab, setSelectedFile]);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    if (!showPlanRestriction && creditLimitCrossed) {
      handleStopAndClose();
    }
  }, [showPlanRestriction, creditLimitCrossed, handleStopAndClose]);

  // Code initialization effects
  useEffect(() => {
    if (tab === "Code" && !isCodeInitialized) {
      setIsCodeInitialized(true);
    }
  }, [tab, isCodeInitialized]);

  useEffect(() => {
    if (activeTab === "Code" && !isCodeViewLoaded) {
      setCodeViewLoaded(true);
    }
  }, [activeTab, isCodeViewLoaded]);

  // Removed live status checking - not needed for basic functionality

  if (!isVisible) return null;

  // Add this helper function before the return statement
  const isCodeOrPreview = (tab) => tab === "Code" || tab === "Preview";

  const TaskProgresstext = ({ completedTasks, totalTasks }) => {
    // Keep it simple with just the core functionality
    return (
      <div className="inline-flex items-center justify-center px-3 cursor-pointer typography-body-sm font-weight-medium border-2 border-primary-400 text-primary-600 rounded">
        {completedTasks}/{totalTasks} Tasks
      </div>
    );
  };

  // Deploy panel click outside handler with deployment context
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        deployPanelRef.current &&
        !deployPanelRef.current.contains(event.target)
      ) {
        setIsDeployPanelOpen(false);
      }
    };

    if (isDeployPanelOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDeployPanelOpen, setIsDeployPanelOpen]);

  // Fetch repositories when component mounts - now handled by the deployment context
  useEffect(() => {
    if (wsConnection?.readyState === WebSocket.OPEN && isDeployPanelOpen) {
      fetchRepositories();
    }
  }, [wsConnection, isDeployPanelOpen, fetchRepositories]);

  // Add this function to handle showing deployments
  const handleShowDeployments = () => {
    setShowDeploymentsModal(!showDeploymentsModal);
    if (!showDeploymentsModal) {
      fetchDeployments();
    }
  };

  // Add this function to handle showing container details
  const handleShowContainerDetails = () => {
    setShowContainerDetailsPanel(!showContainerDetailsPanel);
    
    // If opening the panel and we don't have manifest data, fetch it
    if (!showContainerDetailsPanel && !manifest) {
      setIsLoadingManifest(true);
      fetchManifest();
    }
  };

  // Add this function to handle container selection for deployment
  const handleContainerSelect = (container) => {
    setSelectedContainerForDeployment(container);
    setShowContainerDetailsPanel(false);
    
    // Update query parameters with container type
    const currentParams = new URLSearchParams(searchParams);
    currentParams.set('type', container.type);
    
    // Update the URL with the new query parameter
    window.history.replaceState(
      null,
      '',
      `${window.location.pathname}?${currentParams.toString()}`
    );
    
    // Open the deployment interface with the selected container
    setIsDeploymentConfigOpen(true);
  };

  // Add click outside handler for deployments dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        showDeploymentsModal &&
        !event.target.closest(".deployments-dropdown")
      ) {
        setShowDeploymentsModal(false);
      }
      
      // Also handle container details panel
      if (
        showContainerDetailsPanel &&
        !event.target.closest(".deployments-dropdown")
      ) {
        setShowContainerDetailsPanel(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDeploymentsModal, showContainerDetailsPanel]);

  // Add this function to fetch deployments
  const fetchDeployments = async () => {
    setIsLoadingDeployments(true);
    try {
      const response = await listDeployments(projectId);
      // Sort deployments by created_at in descending order (latest first)
      const sortedDeployments = response.sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at)
      );
      setDeployments(sortedDeployments);
    } catch (error) {
      showAlert("Failed to fetch deployments", "error");
    } finally {
      setIsLoadingDeployments(false);
    }
  };

  // Add delete deployment function
  const handleDeleteDeployment = async (deploymentId, appId) => {
    try {
      await deleteDeployment(projectId, appId);
      showAlert("Deployment deleted successfully", "success");
      // Refresh deployments list
      fetchDeployments();
    } catch (error) {
      showAlert("Failed to delete deployment", "error");
    }
  };

  // Add confirmation dialog state
  const [deleteConfirmation, setDeleteConfirmation] = useState(null);

  // Share the WebSocket connection with the deployment context
  useEffect(() => {
    if (wsConnection) {
      setDeploymentWsConnection(wsConnection);
    }
  }, [wsConnection, setDeploymentWsConnection]);

  // Fetch manifest when modal opens and WebSocket is ready
  useEffect(() => {
    if (isVisible && wsConnection?.readyState === WebSocket.OPEN && currentTaskId && fetchManifest) {
      fetchManifest();
    }
  }, [isVisible, wsConnection, currentTaskId, fetchManifest]);

  // Reset manifest loading state when manifest is received
  useEffect(() => {
    if (manifest) {
      setIsLoadingManifest(false);
    }
  }, [manifest]);

  // No need to monitor isDeploymentConfigOpen changes

  // Add status map for TaskStatus component
  const statusMap = {
    running: {
      text: "Running",
      className:
        "px-2 py-1 typography-caption font-weight-medium bg-green-100 text-green-800 rounded-md shadow-sm",
    },
    paused: {
      text: "Paused",
      className:
        "px-2 py-1 typography-caption font-weight-medium bg-yellow-100 text-yellow-800 rounded-md shadow-sm",
    },
    submitted: {
      text: "Submitted",
      className:
        "px-2 py-1 typography-caption font-weight-medium bg-primary-100 text-primary-800 rounded-md shadow-sm",
    },
    failed: {
      text: "Failed",
      className:
        "px-2 py-1 typography-caption font-weight-medium bg-red-100 text-red-800 rounded-md shadow-sm",
    },
    in_progress: {
      text: "In Progress",
      className:
        "px-2 py-1 typography-caption font-weight-medium bg-primary-100 text-primary-800 rounded-md shadow-sm",
    },
    stopped: {
      text: "Stopped",
      className:
        "px-2 py-1 typography-caption font-weight-medium bg-gray-100 text-gray-800 rounded-md shadow-sm",
    },
  };

  // Add TaskStatus component
  const TaskStatus = ({ taskStatus }) => {
    const normalizedStatus = String(taskStatus || '').toLowerCase().replace(/\s+/g, "_");
    const status = statusMap[normalizedStatus] || statusMap["in_progress"];

    return <div className={status.className}>{status.text}</div>;
  };

  // Add a handler for model selection
  const handleModelSelect = (modelId) => {
    setLlmModel(modelId);

    // Send model selection to the server
    if (wsConnection?.readyState === WebSocket.OPEN) {
      wsConnection.send(
        JSON.stringify({
          type: "set_model",
          task_id: currentTaskId,
          model: modelId,
          user_id: Cookies.get('userId')
        })
      );
    }
  };

  // Request available models and current model when system is ready
  useEffect(() => {
    if (
      isReady &&
      wsConnection?.readyState === WebSocket.OPEN &&
      currentTaskId
    ) {
      // Set loading state
      setIsLoadingModels(true);

      // Request available models
      wsConnection.send(
        JSON.stringify({
          type: "get_available_models",
          task_id: currentTaskId,
          user_id: Cookies.get('userId')
        })
      );

      // Request current model
      wsConnection.send(
        JSON.stringify({
          type: "get_current_model",
          task_id: currentTaskId,
          user_id: Cookies.get('userId')
        })
      );

    }
  }, [isReady, wsConnection, currentTaskId]);

  // Add a direct reset function for immediate loader stopping
  const forceResetAllStates = useCallback(() => {
    setActionLoading(false);
    setIsTerminating(false);
    setIsUpdating(false);
    setIsMerging(false);
    setIsMergeAndTerminate(false);
    if (terminateTimeoutId) {
      clearTimeout(terminateTimeoutId);
      setTerminateTimeoutId(null);
      activeTimersRef.current.delete(terminateTimeoutId);
    }
  }, [terminateTimeoutId, isTerminating, isUpdating, isMerging]);

  // Handle WebSocket responses for models and status updates
  useEffect(() => {
    if (!wsConnection) return;

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // Handle status updates from various WebSocket message types
        if (data.type === "status_update" && data.status) {
          setCurrentTaskStatus(data.status);
        }

        // Handle task status from other message types
        if (data.task_status) {
          setCurrentTaskStatus(data.task_status);
        }

        // Handle status from data object
        if (data.data && data.data.status) {
          setCurrentTaskStatus(data.data.status);
        }

        // Handle error messages - especially agent_not_found during termination
        if (data.type === "error" && data.status === "agent_not_found" && (isTerminating || isMerging)) {

          // Force reset all loading states
          forceResetAllStates();

          // Clear any active timeouts
          if (terminateTimeoutId) {
            clearTimeout(terminateTimeoutId);
            setTerminateTimeoutId(null);
            activeTimersRef.current.delete(terminateTimeoutId);
          }

          // Reset deployment context states
          setIsDeploymentConfigOpen(false);
          setIsDeployPanelOpen(false);
          setIsDeploying(false);
          setShowDeploymentSuccess(false);
          setDeploymentWsConnection(null);

          // Close modal immediately
          setShowConfirmModal(false);
          closeModal();


          return; // Exit early to prevent further processing
        }

        // Handle other error messages during termination operations
        if (data.type === "error" && (isTerminating || isMerging)) {


          // Force reset all loading states
          forceResetAllStates();

          // Clear any active timeouts
          if (terminateTimeoutId) {
            clearTimeout(terminateTimeoutId);
            setTerminateTimeoutId(null);
            activeTimersRef.current.delete(terminateTimeoutId);
          }

          // Reset deployment context states
          setIsDeploymentConfigOpen(false);
          setIsDeployPanelOpen(false);
          setIsDeploying(false);
          setShowDeploymentSuccess(false);
          setDeploymentWsConnection(null);

          // Close modal immediately
          setShowConfirmModal(false);
          closeModal();

          // Show error message
          const errorMessage = data.message || "Unknown error occurred during termination";
          showAlert(`Session terminated - ${errorMessage}`, "error");

          if (isMergeAndTerminate) {
            setIsMergeAndTerminate(false);
          }

          return; // Exit early to prevent further processing
        }

        // Handle model responses
        if (data.type === "available_models" && data.data?.models) {
          setAvailableModels(data.data.models);
          setIsLoadingModels(false);
        } else if (data.type === "current_model" && data.data?.model) {
          setLlmModel(data.data.model);
          setIsLoadingModels(false);
        } else if (data.type === "status_update") {
          // Log all status updates for debugging







          const status = data.data?.status || data.status;
          const isStoppedStatus = status && (
            String(status).toLowerCase() === "stopped" ||
            String(status).toLowerCase() === "stop" ||
            String(status).toLowerCase() === "terminated"
          );




          if (isStoppedStatus) {


            // Immediately call force reset to stop all loaders
            forceResetAllStates();

            // Clear timeout regardless of isTerminating state
            if (terminateTimeoutId) {

              clearTimeout(terminateTimeoutId);
              setTerminateTimeoutId(null);
              activeTimersRef.current.delete(terminateTimeoutId);
            }

            // Force reset loading states if we're in termination process
            if (isTerminating || isUpdating || showConfirmModal || isMergeAndTerminate) {



              const finalSessionName = sessionInputValue.trim() || "Untitled";

              // Reset deployment context states
              setIsDeploymentConfigOpen(false);
              setIsDeployPanelOpen(false);
              setIsDeploying(false);
              setShowDeploymentSuccess(false);
              setDeploymentWsConnection(null);

              // Close modal immediately
              setShowConfirmModal(false);
              closeModal();

              // Reset merge flag if this was after a merge
              if (isMergeAndTerminate) {
                setIsMergeAndTerminate(false); // Reset the flag
              }

              // Update task with final session name in background (don't wait for it)

              updateTask(currentTaskId, { session_name: finalSessionName })
                .then(() => {

                  if (isComponentMountedRef.current) {
                    setSessionName(finalSessionName);
                  }
                })
                .catch((error) => {
                  console.error("Background task update failed:", error);
                  // Don't show error to user since modal is already closed
                });

            } else {

            }
          }
        } else if (data.type === "app_state") {
          // Simply listen for app_state messages and update UI accordingly
          if (data.data?.url) {
            // Set preview URL when received from websocket
            dispatchUiState({
              type: 'BATCH_UPDATE',
              updates: {
                previewUrl: data.data.url,
                isPreviewLoading: false,
              }
            });

            // Also update showUrl if we're on the Preview tab
            if (activeTab === "Preview") {
              dispatchUiState({
                type: 'BATCH_UPDATE',
                updates: { showUrl: data.data.url }
              });
            }
          } else if (data.data?.error) {
            dispatchUiState({
              type: 'BATCH_UPDATE',
              updates: { previewError: data.data.error, isPreviewLoading: false }
            });
          }
        }
      } catch (error) {
        // Silently handle WebSocket message parsing errors
      }
    };

    wsConnection.addEventListener("message", handleMessage);

    return () => {
      wsConnection.removeEventListener("message", handleMessage);
    };
  }, [wsConnection, activeTab, setLlmModel, isTerminating, sessionInputValue, currentTaskId, setSessionName, setIsDeploymentConfigOpen, setIsDeployPanelOpen, setIsDeploying, setShowDeploymentSuccess, setDeploymentWsConnection, showAlert, closeModal, terminateTimeoutId, showConfirmModal, isUpdating, forceResetAllStates, isMergeAndTerminate, setCurrentTaskStatus, addTimerToCleanup]);

  // Add a function to toggle Swagger view
  const toggleSwaggerView = () => {
    setIsSwaggerViewActive(!isSwaggerViewActive);
  };

  const options = [
    {
      key: "continue",
      label: "Continue Session",
      description: "Session remains active for future work. Branch stays separate.",
      actionText: "Continue Session",
      handler: confirmCloseModal,
    },
    {
      key: "discard",
      label: "Discard & Exit",
      description: "End session immediately. All branch changes will be lost.",
      actionText: "Discard & Exit",
      handler: handleStopAndClose,
    },
    {
      key: "save",
      label: "Save & Exit",
      description: "Merge branch to kavia-main, then close session.",
      actionText: "Save & Exit",
      handler: handleMerge,
      disabled: String(currentTaskStatus || '').toLowerCase().replace(/[-\s]/g, '_') === "stopped" || String(currentTaskStatus || '').toLowerCase().replace(/[-\s]/g, '_') === "completed",
    },
  ];

  // Auto-select a different option if the currently selected option becomes disabled
  useEffect(() => {
    const selected = options.find(opt => opt.key === selectedOption);
    if (selected && selected.disabled) {
      // Find the first non-disabled option
      const firstAvailableOption = options.find(opt => !opt.disabled);
      if (firstAvailableOption) {
        setSelectedOption(firstAvailableOption.key);
      }
    }
  }, [currentTaskStatus, selectedOption, options]);

  const selected = options.find(opt => opt.key === selectedOption);

  return (
    <>
      <GitSidebar
        isOpen={isSidebarOpen}
        onClose={closeSidebar}
        repository={currentRepository || {}}
        isConnected={true}
        username="fe-dev"
      />
      <TaskProgress
        isOpen={isTaskProgressOpen}
        onClose={() => setIsTaskProgressOpen(false)}
        tasks={tasks}
      />
      <DeploymentInterface
        isOpen={isDeploymentConfigOpen}
        onClose={() => {
          // Ensure context states are properly reset
          setIsDeploymentConfigOpen(false);

          // If deployment was canceled, also reset deployment progress state
          if (isDeploying) {
            setIsDeploying(false);
          }
        }}
      />
      <div className="fixed inset-0 bg-white bg-opacity-50 z-50" />
      <div className="fixed discussion-modal inset-0 bg-gray-800 bg-opacity-75 flex justify-center items-center z-50 w-full h-full">
        <div
          className={`bg-white w-[95%] h-[95%] p-2 rounded-md flex flex-col relative`}
        >
          <div className="w-full h-[5%] rounded-t-md bg-white flex items-center px-3 py-2 border-b border-gray-200 justify-between">
            <div className="flex items-center space-x-3">
              {currentTaskId?.startsWith("deep-query-job") ? (
                <>
                  <div className="font-weight-medium typography-body-sm text-gray-800">
                    Deep Analysis
                  </div>
                  <BootstrapTooltip
                    title={`WebSocket: ${wsStatus}`}
                    placement="bottom"
                  >
                    <div
                      className={`h-2.5 w-2.5 mb-1 rounded-full animate-pulse
                      ${wsStatus === "connected"
                          ? "bg-green-500"
                          : wsStatus === "connecting"
                            ? "bg-yellow-500 "
                            : "bg-red-500"
                        }`}
                    />
                  </BootstrapTooltip>
                </>
              ) : (
                <>
                  <BootstrapTooltip
                    title={`WebSocket: ${wsStatus}`}
                    placement="bottom"
                  >
                    <div
                      className={`h-2.5 w-2.5 mb-1 rounded-full animate-pulse
                      ${wsStatus === "connected"
                          ? "bg-green-500"
                          : wsStatus === "connecting"
                            ? "bg-yellow-500 "
                            : "bg-red-500"
                        }`}
                    />
                  </BootstrapTooltip>
                  <div className="font-weight-medium typography-body-sm text-gray-800">
                    {currentTaskId?.startsWith("cm")
                      ? "Code Maintenance"
                      : "Code Generation"}
                  </div>
                </>
              )}
              {!currentTaskId?.startsWith("deep-query-job") && (
                <div onClick={() => setIsTaskProgressOpen(!isTaskProgressOpen)}>
                  <TaskProgresstext
                    completedTasks={
                      steps.filter((step) => step.status === "completed").length
                    }
                    totalTasks={steps.length}
                  />
                </div>
              )}

              {/* Add ModelSelector here */}
              {/* {!currentTaskId?.startsWith("deep-query-job") && ( */}
              <ModelSelector
                selectedModel={llmModel}
                onSelectModel={handleModelSelect}
                availableModels={availableModels}
                isDisabled={!isReady || availableModels.length === 0}
                isLoading={isLoadingModels}
              />
              {/* )} */}
            </div>

            <div className="flex items-center space-x-3">
                          {subscriptionData && tenantId === 'b2c' ? (
  <CreditBadge
    planCredits={subscriptionData.planCredits}
    organizationCost={subscriptionData.organizationCost}
    isRefreshing={isRefreshingPlan}
    onRefresh={handleRefreshPlan}
  />
) : null}
              <TaskStatus taskStatus={currentTaskStatus || taskDetails?.status || "unknown"} />
              {/* Download and View Logs Section */}
              <div className="flex items-center space-x-1">
                <BootstrapTooltip title="Download logs" placement="bottom">
                  <button
                    onClick={handleDownloadLogs}
                    className="flex items-center gap-1 px-2 py-1 typography-caption font-weight-medium text-gray-700 rounded-md hover:bg-gray-50 transition-colors group"
                  >
                    <Download className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    <span className="group-hover:hidden">Logs</span>
                    <span className="hidden group-hover:inline">Download</span>
                  </button>
                </BootstrapTooltip>
              </div>

              {/* <BootstrapTooltip title="View logs" placement="bottom">
                {currentTaskId && (
                  <a
                    href={`https://us5.datadoghq.com/logs?query=task_id%3A${currentTaskId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 px-2 py-1 typography-caption font-weight-medium text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Log
                    <ExternalLink className="w-3 h-3" />
                  </a>
                )}
              </BootstrapTooltip> */}

              {!currentTaskId?.startsWith("deep-query-job") && (
                <BootstrapTooltip title={
                  (currentTaskStatus === "completed" || currentTaskStatus === "submitted" || currentTaskStatus === "failed" || currentTaskStatus === "paused")
                    ? "Git Dashboard (Available when task is running)"
                    : "Open Git Dashboard"
                } placement="bottom">
                  <button
                    onClick={(currentTaskStatus === "completed" || currentTaskStatus === "submitted" || currentTaskStatus === "failed"  || currentTaskStatus === "paused") 
                      ? undefined 
                      : openSidebar
                    }
                    disabled={currentTaskStatus === "completed" || currentTaskStatus === "SUBMITTED" || currentTaskStatus === "failed" || currentTaskStatus === "paused"}
                    className={`p-1 rounded-md transition-colors ${
                      (currentTaskStatus === "completed" || currentTaskStatus === "submitted" || currentTaskStatus === "failed" || currentTaskStatus === "paused")
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:bg-gray-50 cursor-pointer"
                    }`}
                  >
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M15.951 7.34C10.8862 7.34 6.78027 11.4459 6.78027 16.5107C6.78027 20.5627 9.40748 24 13.0524 25.2122C13.511 25.2964 13.6785 25.0128 13.6785 24.7708C13.6785 24.5526 13.6704 23.8291 13.6655 23.064C11.1151 23.6182 10.5764 21.9826 10.5764 21.9826C10.1587 20.9232 9.55789 20.6412 9.55789 20.6412C8.72411 20.0722 9.62083 20.0837 9.62083 20.0837C10.5413 20.1474 11.0268 21.0286 11.0268 21.0286C11.8451 22.4305 13.1742 22.0251 13.1742 22.0251C13.6957 21.7897 13.7791 21.1987 14.0161 20.7932C14.2777 20.5651 12.2423 20.3338 10.1007 19.5466C10.1007 16.0325 10.1007 15.0303 10.4587 14.2137C11.044 13.5712 10.9508 13.3383 10.6344 12.4056C11.1347 11.1435 11.1347 11.1435 11.9039 10.8974C13.6565 12.0835 14.3872 11.8792 15.172 11.7786C15.951 11.7754 16.73 11.7786 17.5147 11.8808C18.2471 12.0852 19.9964 10.8974 20.7673 11.1451C20.7673 11.1451 21.2683 12.408 20.9528 13.3399C20.8588 13.5721 21.4465 14.2137 21.8013 15.0312C21.8013 16.0333 21.8013 19.5564 19.6564 20.3322C17.6136 20.5594 17.9439 20.8439 18.2357 21.4014C18.2357 22.2564 18.2357 23.4825 18.2234 24.4708C18.2234 24.7733 18.2234 25.0177 18.3902 25.303C18.8545 25.213 22.4961 23.9983 25.1209 20.5619C25.1209 16.5115 25.1209 11.4476 21.0149 7.34081C15.9502 7.34081 15.951 7.34Z"
                        fill="#4B5563"
                      />
                    </svg>
                  </button>
                </BootstrapTooltip>
              )}
              {!currentTaskId?.startsWith("deep-query-job") && (
                <div className="relative deployments-dropdown">
                  {/* <BootstrapTooltip title={isExpanded ? "Collapse" : "Expand"} placement="bottom"> */}
                  <div className="flex items-center space-x-2 px-3 h-[32px] typography-caption font-weight-medium bg-primary text-white hover:bg-primary-600 rounded-md shadow-sm transition-colors">
                    <button onClick={handleShowDeployments} className="">
                      Deploy
                      {deployments.length != 0 ? ` (${deployments.length})` : ""}
                    </button>
                    <button
                      onClick={handleShowContainerDetails}
                      className="border-l pl-2 h-8 border-white"
                    >
                      <Plus size={16} strokeWidth={2.75} className="" />
                    </button>
                  </div>
                  {/* </BootstrapTooltip> */}
                  {showDeploymentsModal && (
                    <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                      <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                            <span className="w-2 h-2 rounded-full bg-primary"></span>
                            Deployment History
                          </h3>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setIsLoadingDeployments(true);
                                listDeployments(projectId)
                                  .then((response) => {
                                    const sortedDeployments = response.sort(
                                      (a, b) => new Date(b.created_at) - new Date(a.created_at)
                                    );
                                    setDeployments(sortedDeployments);
                                  })
                                  .catch((error) => {
                                    showAlert("Failed to fetch deployments", "error");
                                  })
                                  .finally(() => {
                                    setIsLoadingDeployments(false);
                                  });
                              }}
                              className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                              title="Refresh deployments"
                            >
                              <RefreshCw size={16} className={`${isLoadingDeployments ? 'animate-spin' : ''}`} />
                            </button>
                          <button
                            onClick={() => setShowDeploymentsModal(false)}
                            className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                          >
                            <X size={16} />
                          </button>
                          </div>
                        </div>
                      </div>
                      <div className="max-h-[300px] overflow-y-auto p-4">
                        {isLoadingDeployments ? (
                          <div className="flex justify-center items-center py-4">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="ml-2 text-gray-600">Loading deployments...</span>
                          </div>
                        ) : deployments.length === 0 ? (
                          <div className="text-center py-8 px-4">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                              <ExternalLink className="h-8 w-8 text-gray-400" />
                            </div>
                            <p className="text-base font-medium text-gray-700">No deployments found</p>
                            <p className="text-sm text-gray-500 mt-1">There are no deployments for this session yet.</p>
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {deployments.map((deployment) => (
                              <div
                                key={deployment.deployment_id}
                                className="bg-white rounded-lg p-3 border border-gray-200 hover:border-primary-200 transition-all duration-200 cursor-pointer"
                                onClick={() => {
                                  const deploymentUrl = deployment.custom_domain || deployment.app_url;
                                  if (deploymentUrl) {
                                    window.open(deploymentUrl, '_blank');
                                  }
                                }}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate text-left">
                                      {deployment.root_path
                                        ? deployment.root_path.split("/").filter(Boolean).pop()
                                        : "Unknown Folder"}
                                    </p>
                                    <div className="flex items-center gap-2 mt-1">
                                      <p className="text-xs text-gray-500 flex items-center gap-1 text-left">
                                        <span className="inline-block w-2 h-2 rounded-full bg-gray-300"></span>
                                        <span className="truncate">{deployment.branch_name || "Default Branch"}</span>
                                      </p>
                                      {(deployment.custom_domain || deployment.app_url) && (
                                        <a
                                          href={deployment.custom_domain || deployment.app_url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-primary hover:text-primary-700 flex-shrink-0"
                                          onClick={(e) => e.stopPropagation()}
                                          title={deployment.custom_domain || deployment.app_url}
                                        >
                                          <ExternalLink size={12} />
                                        </a>
                                      )}
                                    </div>
                                  </div>
                                  <span className={`flex-shrink-0 px-2 py-1 text-xs font-medium rounded-full whitespace-nowrap border ${
                                    deployment.status === "success" && deployment.domain_status
                                      ? deployment.domain_status === "available"
                                        ? "bg-green-50 text-green-700 border-green-200"
                                        : deployment.domain_status === "inprogress"
                                        ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                        : "bg-gray-50 text-gray-700 border-gray-200"
                                      : deployment.status === "success"
                                      ? "bg-green-50 text-green-700 border-green-200"
                                      : deployment.status === "processing"
                                      ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                      : "bg-red-50 text-red-700 border-red-200"
                                    }`}>
                                    {deployment.status === "success" && deployment.domain_status === "inprogress"
                                      ? "processing"
                                      : deployment.status}
                                  </span>
                                </div>
                                {deployment.status === "success" && deployment.domain_status === "inprogress" && (
                                  <div className="flex items-center gap-2 mt-2">
                                    <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                                    <p className="text-xs text-yellow-700 font-medium text-left">
                                      Domain is processing
                                    </p>
                                  </div>
                                )}
                                {deployment.status === "success" && (deployment.domain_status === "success" || deployment.domain_status === "available") && (
                                  <div className="flex items-center gap-2 mt-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <p className="text-xs text-green-700 font-medium text-left">
                                      Your app is live now
                                    </p>
                                  </div>
                                )}
                                <div className="mt-2 text-xs text-gray-500 text-left">
                                  {new Date(deployment.created_at).toLocaleDateString()}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Container Details Panel */}
                  {showContainerDetailsPanel && (
                    <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                      <div className="p-3 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                          <h3 className="typography-body-sm font-weight-medium text-gray-900">
                            Select Container for Deployment
                          </h3>
                          <button
                            onClick={() => setShowContainerDetailsPanel(false)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <X size={14} />
                          </button>
                        </div>
                      </div>
                      <div className="max-h-[300px] overflow-y-auto p-3">
                        {isLoadingManifest ? (
                          <div className="text-center py-8">
                            <div className="mb-3">
                              <div className="inline-block h-6 w-6 rounded-full border-2 border-primary border-t-transparent animate-spin mx-auto"></div>
                            </div>
                            <p className="typography-body-sm text-gray-500 mb-2">
                              Loading container information...
                            </p>
                            <p className="typography-caption text-gray-400">
                              Fetching deployment manifest from project
                            </p>
                          </div>
                        ) : manifest && (manifest.backend || manifest.frontend) ? (
                          <div className="space-y-2">
                            {/* Backend Container */}
                            {manifest.backend && (
                              <div
                                className="bg-gray-50 rounded-md p-3 border border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors hover:border-primary-300 cursor-pointer"
                                onClick={() => handleContainerSelect(manifest.backend)}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="w-2 h-2 rounded-full bg-primary"></div>
                                      <p className="typography-body-sm font-weight-medium text-gray-900">
                                        {manifest.backend.container_name}
                                      </p>
                                      <span className="px-2 py-1 typography-caption bg-primary-100 text-primary-800 rounded-full">
                                        Backend
                                      </span>
                                    </div>
                                    <p className="typography-caption text-gray-500 mb-1">
                                      Framework: {manifest.backend.framework}
                                    </p>
                                    {manifest.backend.ports && (
                                      <p className="typography-caption text-gray-500">
                                        Port: {manifest.backend.ports}
                                      </p>
                                    )}
                                    {manifest.backend.container_details?.features && (
                                      <p className="typography-caption text-gray-500 mt-1">
                                        Features: {manifest.backend.container_details.features.slice(0, 2).join(', ')}
                                        {manifest.backend.container_details.features.length > 2 && '...'}
                                      </p>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Rocket size={14} className="text-primary" />
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Frontend Container */}
                            {manifest.frontend && (
                              <div
                                className="bg-gray-50 rounded-md p-3 border border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors hover:border-primary-300"
                                onClick={() => handleContainerSelect(manifest.frontend)}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                      <p className="typography-body-sm font-weight-medium text-gray-900">
                                        {manifest.frontend.container_name}
                                      </p>
                                      <span className="px-2 py-1 typography-caption bg-green-100 text-green-800 rounded-full">
                                        Frontend
                                      </span>
                                    </div>
                                    <p className="typography-caption text-gray-500 mb-1">
                                      Framework: {manifest.frontend.framework}
                                    </p>
                                    {manifest.frontend.ports && (
                                      <p className="typography-caption text-gray-500">
                                        Port: {manifest.frontend.ports}
                                      </p>
                                    )}
                                    {manifest.frontend.container_details?.features && (
                                      <p className="typography-caption text-gray-500 mt-1">
                                        Features: {manifest.frontend.container_details.features.slice(0, 2).join(', ')}
                                        {manifest.frontend.container_details.features.length > 2 && '...'}
                                      </p>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Rocket size={14} className="text-primary" />
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <div className="mb-3">
                              <Rocket size={32} className="text-gray-400 mx-auto" />
                            </div>
                            <p className="typography-body-sm text-gray-500 mb-2">
                              No containers available
                            </p>
                            <p className="typography-caption text-gray-400">
                              No deployment containers found in project manifest
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* {renderDeployPanel()} */}
              <BootstrapTooltip title="Close" placement="bottom">
                <button
                  onClick={handleConfirmClose}
                  className="ml-1 p-1 text-gray-500 hover:text-gray-700 rounded-md transition-colors flex-shrink-0"
                >
                  <X size={20} />
                </button>
              </BootstrapTooltip>
            </div>
          </div>
          <div className="flex h-[93%] overflow-hidden">
            <div className="flex flex-grow overflow-hidden px-3 py-2 pb-0">
              {/* Left Panel */}
              <div
                className={`
    ${isPanelExpanded
                    ? "min-w-[100%] max-w-[100%]"
                    : "min-w-[30%] max-w-[30%]"
                  }
    transition-all duration-300 ease-in-out relative overflow-hidden
  `}
              >
                {/* Toggle Buttons */}
                <div
                  className="absolute -right-4 top-1/2 transform -translate-y-1/2 z-10"
                >
                  {/* Button code (commented out in your example) */}
                </div>

                {/* Panel Content */}
                <div
                  className={`
      transition-all duration-300 ease-in-out h-full rounded-lg bg-white opacity-100 visible
    `}
                  style={{
                    transitionDelay: "0ms",
                  }}
                >
                  {/* Code generation chat panel */}
                  <CodeDiscussionPanel isPanelExpanded={isPanelExpanded} isStopped={currentTaskStatus === "stopped" || taskDetails?.status === "stopped"} />
                </div>
              </div>

              {/* Right Panel */}
              <div
                className={`flex-grow overflow-auto justify-between transition-all duration-300 ease-in-out ${isFullscreen ? "fixed inset-0 z-50 bg-white p-4" : ""
                  }`}
                ref={rightPanelRef}
              >
                <div className="flex flex-col h-full bg-white border border-gray-200 rounded-lg ml-4">
                  {/* Tabs Header */}
                  <div className="tab-header px-2 py-[8px] bg-white rounded-t-lg flex items-center justify-between border border-[#DEDEDE]">
                    {/* LEFT: Code and Preview tabs */}
                    <div className="tab-buttons flex rounded-[6px] overflow-hidden border border-gray-200">
                      <TabButtons
                        currentTaskId={currentTaskId}
                        activeTab={activeTab}
                        toggleTab={toggleTab}
                        documentContent={documentContent}
                        newDocAlert={newDocAlert}
                      />
                    </div>

                    {activeTab === "Preview" && (
                      <PreviewContainers />
                    )}

                    {/* MIDDLE: URL display with refresh and copy buttons */}
                    <UrlDisplaySection
                      currentTaskId={currentTaskId}
                      activeTab={activeTab}
                      showUrl={showUrlRef.current}
                      handleRefresh={handleRefresh}
                      handleCopy={handleCopy}
                      copied={copied}
                    />

                    {/* RIGHT: Action buttons */}
                    <TabActionButtons
                      currentTaskId={currentTaskId}
                      activeTab={activeTab}
                      showUrl={showUrlRef.current}
                      activeView={activeView}
                      setActiveView={setActiveView}
                      isSwaggerViewActive={isSwaggerViewActive}
                      toggleSwaggerView={toggleSwaggerView}
                      isFullscreen={isFullscreen}
                      toggleFullscreen={toggleFullscreen}
                    />
                  </div>
                  {/* Tab Content */}
                  <div className="flex-grow overflow-auto">
                    <div className="tab-content h-full w-full relative overflow-hidden">
                      <TabContent
                        currentTaskId={currentTaskId}
                        activeTab={activeTab}
                        documentContent={documentContent}
                        isSwaggerViewActive={isSwaggerViewActive}
                        formatWorkspaceUrl={formatWorkspaceUrl}
                        currentIp={currentIp}
                        isPreviewLoading={uiState.isPreviewLoading}
                        previewError={uiState.previewError}
                        handlePreviewRefresh={handlePreviewRefresh}
                        handlePortChange={handlePortChange}
                        portNumber={portNumber}
                        iframeKey={iframeKey}
                        previewUrl={uiState.previewUrl}
                        currentUrl={uiState.currentUrl}
                        activeView={activeView}
                        isLoading={isLoading}
                        setIsLoading={setIsLoading}
                        projectId={projectId}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification.show && (
        <div className="fixed bottom-10 right-10 z-[60] animate-fade-in">
          <div className="bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>{notification.message}</span>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          {/* Backdrop overlay */}
          <div
            className="fixed inset-0 bg-gray-800 bg-opacity-75 backdrop-blur-sm"
            onClick={() => setShowConfirmModal(false)}
          />

          {/* Modal container */}
          <div
            className={`bg-white rounded-lg shadow-xl max-w-md w-full p-6 z-[70] relative `}
          >
            {/* Close button */}
            <button
              onClick={() => setShowConfirmModal(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={18} />
            </button>

            {/* Modal content */}
            <div className="space-y-5">
              {/* Title and description */}
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900">Close Session</h3>
                <p className="mt-2 text-sm text-gray-500">Choose how you'd like to handle this session</p>
              </div>
              {/* Session name and warning only for non-discard options */}

              <>
                <div className="space-y-2 mb-4">
                  <label className="block typography-body-sm font-weight-medium text-gray-700">
                    Session name (optional)
                  </label>
                  <input
                    type="text"
                    value={sessionInputValue}
                    onChange={(e) => setSessionInputValue(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 transition-colors focus:border-primary focus:ring-primary-500"
                    placeholder="Name your session"
                  />
                </div>
                {/* <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <FaInfoCircle className="h-5 w-5 text-primary-500" />
                    </div>
                    <div className="ml-3">
                      <p className="typography-body-sm text-gray-700">
                        {currentTaskId?.startsWith("deep-query-job")
                          ? "Make sure to save important query results before closing."
                          : "Make sure to commit and push your code using the Git Dashboard before closing."}
                      </p>
                    </div>
                  </div>
                </div> */}
              </>

              <div className="space-y-3">
                {options.map(opt => {
                  if (!currentTaskId?.startsWith('deep-query-job') || currentTaskId?.startsWith('deep-query-job') && opt.key !== 'save') {
                    return (
                      <div
                        key={opt.key}
                        className={`flex items-start p-4 rounded-lg border transition
                        ${opt.disabled 
                          ? "border-gray-200 bg-gray-50 cursor-not-allowed opacity-50" 
                          : selectedOption === opt.key
                            ? "border-primary bg-primary-50 cursor-pointer"
                            : "border-gray-200 bg-white hover:bg-gray-50 cursor-pointer"
                          }`}
                        onClick={() => !opt.disabled && setSelectedOption(opt.key)}
                      >
                        <input
                          type="radio"
                          checked={selectedOption === opt.key}
                          onChange={() => !opt.disabled && setSelectedOption(opt.key)}
                          disabled={opt.disabled}
                          className="mt-1 accent-orange-500"
                        />
                        <div className="ml-3">
                          <div className={`font-semibold ${
                            opt.disabled 
                              ? "text-gray-400" 
                              : selectedOption === opt.key 
                                ? "text-primary-700" 
                                : "text-gray-900"
                          }`}>
                            {currentTaskId?.startsWith('deep-query-job') && opt.key == 'discard' ? "Save & Exit" : opt.label}
                          </div>
                          <div className={`text-sm ${opt.disabled ? "text-gray-400" : "text-gray-500"}`}>
                            {opt.disabled && opt.key === 'save' 
                              ? "Save & Exit is not available when the session is stopped or completed"
                              : currentTaskId?.startsWith('deep-query-job') && opt.key == 'discard'
                                ? "Save and Close Session" 
                                : opt.description}
                          </div>
                        </div>
                      </div>
                    )
                  }
                })}
              </div>
              {/* Discard warning for discard option */}

              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setShowConfirmModal(false)}
                  className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={selected.disabled ? undefined : selected.handler}
                  disabled={actionLoading || selected.disabled}
                  className="px-4 py-2 text-white bg-primary rounded-md hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {actionLoading && (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  )}
                  {currentTaskId?.startsWith('deep-query-job') && selected.key == 'discard' ? "Save & Exit" : selected.actionText}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Deployment Success Modal */}
      {showDeploymentSuccess && (
        <div className="fixed inset-0 flex items-center justify-center z-[60]">
          <div
            className="fixed inset-0 bg-gray-800 bg-opacity-75"
            onClick={() => setShowDeploymentSuccess(false)}
          />
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 z-[70] relative">
            {/* Close button */}
            <button
              onClick={() => setShowDeploymentSuccess(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={18} />
            </button>

            <div className="space-y-5">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="bg-green-100 rounded-full p-3">
                    <Rocket className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <h3 className="typography-body-lg font-weight-semibold text-gray-900">
                  Deployment Successful!
                </h3>
                <p className="mt-2 typography-body-sm text-gray-500">
                  Your application has been successfully deployed.
                </p>
              </div>

              {/* Deployment URL */}
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="typography-body-sm font-weight-medium text-gray-700 mb-2">
                  Your site is now live at:
                </p>
                <div className="flex items-center">
                  <input
                    type="text"
                    value={deployedUrl}
                    readOnly
                    className="flex-1 px-3 py-2 bg-white border border-gray-300 rounded-l-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary typography-body-sm"
                  />
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(deployedUrl);
                      showAlert("URL copied to clipboard", "success");
                    }}
                    className="px-3 py-2 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors"
                    title="Copy URL"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-gray-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-4 mt-6">
                <button
                  onClick={() => setShowDeploymentSuccess(false)}
                  className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors"
                >
                  Close
                </button>
                <a
                  href={deployedUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="px-4 py-2 typography-body-sm font-weight-medium text-white bg-primary hover:bg-primary-600 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 transition-colors flex items-center justify-center gap-1"
                >
                  <ExternalLink size={14} />
                  Visit Site
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {deleteConfirmation && (
        <div className="fixed inset-0 flex items-center justify-center z-[60]">
          <div
            className="fixed inset-0 bg-gray-800 bg-opacity-75"
            onClick={() => setDeleteConfirmation(null)}
          />
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 z-[70] relative">
            <div className="flex items-center justify-between mb-4">
              <h3 className="typography-body-lg font-weight-semibold text-gray-900">
                Delete Deployment
              </h3>
              <button
                onClick={() => setDeleteConfirmation(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={18} />
              </button>
            </div>
            <p className="typography-body-sm text-gray-600 mb-4">
              Are you sure you want to delete this deployment? This action
              cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setDeleteConfirmation(null)}
                className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-1 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  handleDeleteDeployment(
                    deleteConfirmation.deployment_id,
                    deleteConfirmation.app_id
                  );
                  setDeleteConfirmation(null);
                }}
                className="px-4 py-2 typography-body-sm font-weight-medium text-white bg-red-500 hover:bg-red-600 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      <InactivityTimerModal />

      {/* Deployment Interface */}
      <DeploymentInterface
        isOpen={isDeploymentConfigOpen}
        onClose={() => setIsDeploymentConfigOpen(false)}
        selectedContainer={selectedContainerForDeployment}
      />
    </>
  );
};

export default CodeGenerationModal;
