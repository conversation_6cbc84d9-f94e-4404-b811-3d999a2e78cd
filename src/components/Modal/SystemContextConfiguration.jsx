"use client";

import { useState, useContext, useEffect, useCallback } from "react";
import Logo from "../../../public/logo/kavia_logo.svg";
import { Upload, AlertCircle, CheckCircle } from 'lucide-react';
import Image from "next/image";
import ConfigureModal from "../Modal/ConfigureModel";
import { StateContext } from "../Context/StateContext";
import PropertiesRenderer from "../UiMetadata/PropertiesRenderer";
import {
  fetchSystemContextWithContainers,
  createProjectGuidanceFlow,
  updateNodeByPriority,
} from "@/utils/api";
import { ProjectSetupContext } from "../Context/ProjectSetupContext";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";
import TableComponent from "@/components/SimpleTable/table";
import en from "@/en.json";
import ContainerConfigurationStep from "./ContainerConfigurationStep";
import { ExecutionContext } from "../Context/ExecutionContext";
import StatusPanel from "@/components/StatusPanel/StatusPanel";
import CodeGenerationSetupModal from "@/app/modal/CodeGenerationSetupModal";
import CodeGenerationHandler from "@/app/modal/CodeGenerationHandler";
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { PLATFORMS, frameworks, Generic, backendFrameworks, mobileFrameworks } from "@/constants/code_gen/platforms";
import { BranchSelector } from "@/components/Git/BranchSelector";
import RepositoryDetailsModal from "@/components/Modal/RepositoryModal";
import { getRepository, listAllBranches } from "@/utils/repositoryAPI";
import { buildProjectUrl } from '@/utils/navigationHelpers';


const DISCUSSION_TYPES = [
  "system_context_overview",
  "system_context_containers"
];


const CONFIG_STATE_MAPPING = {
  "system_context_overview": "overview_config_state",
  "system_context_containers": "containers_config_state"
};

export default function SystemContextConfigurationStep({ type }) {
  const [configMethod, setConfigMethod] = useState("discussion");
  const { projectId, setContainerId, showConfigModel, setShowConfigModel, selectedContainerIdVal, setSelectedContainerIdVal, setIsChecked, isChecked } = useContext(ProjectSetupContext);
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [systemContext, setSystemContext] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedContainerId, setSelectedContainerId] = useState(null);
  const [showContainerDetails, setShowContainerDetails] = useState(false);
  const { configStatus, currentTaskId, setAutoNavigateEnabled } = useContext(ExecutionContext)
  const [taskStatus, setTaskStatus] = useState("Idle");
  const [currentDiscussionIndex, setCurrentDiscussionIndex] = useState(0);

  // Code generation state variables
  const [codeGenSetupModal, setCodeGenSetupModal] = useState(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [currentPlatform, setCurrentPlatform] = useState({ key: "generic", label: "Generic", icon: <Generic /> });

  const [currentFramework, setCurrentFramework] = useState(frameworks[0]);
  const [selectedSystemContext, setSelectedSystemContext] = useState({});
  const { isVisible } = useCodeGeneration();

  // Repository state variables
  const [showRepoDetails, setShowRepoDetails] = useState(false);
  const [isRepoConfigured, setIsRepoConfigured] = useState(false);
  const [repositoryState, setRepositoryState] = useState({
    state: 'initial', // initial, loading, success, error
    data: null
  });
  const [containerBranches, setContainerBranches] = useState({});

  const currentDiscussionType = DISCUSSION_TYPES[currentDiscussionIndex];

  const getDiscussionTitle = (type) => {
    switch (type) {
      case "system_context_overview": return "System Context Overview";
      case "system_context_containers": return "Container Configuration";
      default: return "Discussion";
    }
  };


  const { showAlert } = useContext(AlertContext);
  const overviewFlag = sessionStorage.getItem(
    `openSystemContextOverview-${projectId}`
  );
  const containersFlag = sessionStorage.getItem(
    `openSystemContextContainers-${projectId}`
  );

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { setIsVertCollapse } = useContext(StateContext);

  const fetchAllBranchesForContainer = async (containerId, onComplete = null) => {
    const containerKey = `container_${containerId}`;

    try {
      // Set loading state for this container's branches
      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          ...prev[containerKey],
          isFetching: true,
          error: false
        }
      }));

      let allBranches = [];
      let currentPage = 1;
      let totalPages = 1;
      const perPage = 100; // Fetch more branches per page

      // Fetch all pages of branches
      do {
        const response = await listAllBranches(projectId, containerId, currentPage, perPage);

        if (response?.detail === "404: Repository not found") {
          setContainerBranches(prev => ({
            ...prev,
            [containerKey]: {
              branches: [],
              allBranchesFetched: true,
              isFetching: false,
              error: false,
              pagination: { currentPage: 1, totalPages: 1, perPage, totalCount: 0 }
            }
          }));
          return [];
        }

        if (response.branches && response.branches.length > 0) {
          allBranches = [...allBranches, ...response.branches];
        }

        totalPages = response.pagination?.total_pages || 1;
        currentPage++;
      } while (currentPage <= totalPages);

      // Store all branches for this container
      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          branches: allBranches,
          allBranchesFetched: true,
          isFetching: onComplete ? true : false, // Keep loading if onComplete callback is provided
          error: false,
          pagination: {
            currentPage: 1,
            totalPages: Math.ceil(allBranches.length / 30),
            perPage: 30,
            totalCount: allBranches.length
          }
        }
      }));

      // If onComplete callback is provided, call it and let it handle the loading state
      if (onComplete) {
        await onComplete();
        // After onComplete finishes, set loading to false
        setContainerBranches(prev => ({
          ...prev,
          [containerKey]: {
            ...prev[containerKey],
            isFetching: false
          }
        }));
      }

      return allBranches; // Return the branches array
    } catch (error) {
      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          branches: [],
          allBranchesFetched: true,
          isFetching: false,
          error: false,
          pagination: { currentPage: 1, totalPages: 1, perPage: 30, totalCount: 0 }
        }
      }));
      return [];
    }
  };


  const getPaginatedBranches = (containerId, page = 1, pageSize = 30) => {
    const containerKey = `container_${containerId}`;
    const containerData = containerBranches[containerKey];

    if (!containerData || !containerData.branches) {
      return {
        branches: [],
        pagination: { currentPage: 1, totalPages: 1, perPage: pageSize, totalCount: 0 }
      };
    }

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedBranches = containerData.branches.slice(startIndex, endIndex);

    return {
      branches: paginatedBranches,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(containerData.branches.length / pageSize),
        perPage: pageSize,
        totalCount: containerData.branches.length
      }
    };
  };


  // Track URL parameters
  useEffect(() => {
    const hasDiscussionParam = searchParams.has("discussion");
    const isCreatingSystemContext = searchParams.has(
      "is_creating_system_context"
    );
    const nodeId = searchParams.get("node_id");
    const nodeType = searchParams.get("node_type");
    const discussionType = searchParams.get("discussionType");

    if (nodeId && nodeType === "SystemContext" && discussionType) {
      // If the discussion parameter was previously present but now removed
      if (!hasDiscussionParam && !isCreatingSystemContext) {
        // Fetch updated system context to check configuration status
        fetchSystemContextWithContainers(projectId)
          .then((data) => {
            // For overview type
            if (
              discussionType === "system_context_overview" &&
              data?.data?.systemContext?.properties?.overview_config_state ===
              "configured"
            ) {
              // Store flag
              sessionStorage.setItem(
                `openSystemContextOverview-${projectId}`,
                "true"
              );

              // Log to MongoDB with overview step name
              createProjectGuidanceFlow(parseInt(projectId), {
                project_id: parseInt(projectId),
                step_name: "system_context_overview",
                status: "completed",
                data: {
                  system_context_id: parseInt(data.data.systemContext.id),
                  type: "SystemContext",
                  status: "configured",
                },
              })
                .then((result) => {

                  setSystemContext(data);
                })
                .catch((error) => {

                });
            }
            // For containers type
            else if (
              discussionType === "system_context_containers" &&
              data?.data?.containers &&
              data.data.containers.length > 0
            ) {
              // Store flag
              sessionStorage.setItem(
                `openSystemContextContainers-${projectId}`,
                "true"
              );

              // Log to MongoDB with containers step name
              createProjectGuidanceFlow(parseInt(projectId), {
                project_id: parseInt(projectId),
                step_name: "system_context_containers",
                status: "completed",
                data: {
                  system_context_id: parseInt(data.data.systemContext.id),
                  type: "SystemContext",
                  containers_count: data.data.containers.length,
                  status: "configured",
                },
              })
                .then((result) => {

                  setSystemContext(data);
                })
                .catch((error) => {

                });
            }
          })
          .catch((error) => {

          });
      }
    }
  }, [searchParams, projectId]);

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const handleConfigureClick = () => {
    setConfigMethod("auto");
    setConfigureModel(true);
  };
  const cleanDescription = (description) => {
    if (!description) return '';

    return description
      .replace(/#+\s/g, '')        // Remove markdown headers (# Header)
      .replace(/\*\*/g, '')        // Remove bold (**text**)
      .replace(/\*/g, '')          // Remove italics (*text*)
      .replace(/`/g, '')           // Remove code ticks (`code`)
      .replace(/\n\n/g, ' ')       // Replace double line breaks with space
      .replace(/\n-\s/g, ', ')     // Replace bullet points with commas
      .replace(/\n\d+\.\s/g, ', ') // Replace numbered lists with commas
      .replace(/\n/g, ' ')         // Replace remaining line breaks with spaces
      .replace(/\s{2,}/g, ' ')     // Replace multiple spaces with single space
      .trim();                     // Trim extra whitespace
  };

  const updateRequirements = () => {
    if (!projectId) return; // Guard against undefined projectId

    setConfigMethod("discussion");

    // Create new search params
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    if (systemContext?.data?.systemContext?.id) {
      newSearchParams.set("node_id", systemContext.data.systemContext.id);
    }
    newSearchParams.set("node_type", "SystemContext");

    // Set the correct discussionType based on type prop
    if (type === "overview") {
      newSearchParams.set("discussionType", "system_context_overview");
    } else if (type === "container") {
      newSearchParams.set("discussionType", "system_context_containers");
    }

    newSearchParams.set("is_creating_system_context", "true");
    // Ensure projectId is properly stringified
    const correctPath = buildProjectUrl(projectId.toString(), 'architecture/system-context');
    router.push(`${correctPath}?${newSearchParams.toString()}`);
  };

  const getSystemContext = async () => {
    setIsLoading(true);
    try {
      const data = await fetchSystemContextWithContainers(projectId);
      setSystemContext(data);

      // If system context is already configured but flags not set, store in MongoDB
      const relevantFlag =
        type === "overview" ? overviewFlag : containersFlag;

      if (!relevantFlag) {
        // For overview type, check properties
        if (
          type === "overview" &&
          data?.data?.systemContext?.properties?.overview_config_state ===
          "configured"
        ) {
          // Store flag
          sessionStorage.setItem(
            `openSystemContextOverview-${projectId}`,
            "true"
          );

          // Log to MongoDB with overview step name
          try {
            const result = await createProjectGuidanceFlow(
              parseInt(projectId),
              {
                project_id: parseInt(projectId),
                step_name: "system_context_overview",
                status: "completed",
                data: {
                  system_context_id: parseInt(data.data.systemContext.id),
                  type: "SystemContext",
                  status: "configured",
                },
              }
            );

          } catch (error) {

          }
        }
        // For containers type, check for containers array
        else if (
          type === "container" &&
          data?.data?.containers &&
          data.data.containers.length > 0
        ) {
          // Store flag
          sessionStorage.setItem(
            `openSystemContextContainers-${projectId}`,
            "true"
          );

          // Log to MongoDB with containers step name
          try {
            const result = await createProjectGuidanceFlow(
              parseInt(projectId),
              {
                project_id: parseInt(projectId),
                step_name: "system_context_containers",
                status: "completed",
                data: {
                  system_context_id: parseInt(data.data.systemContext.id),
                  type: "SystemContext",
                  containers_count: data.data.containers.length,
                  status: "configured",
                },
              }
            );

          } catch (error) {

          }
        }
      }
    } catch (error) {

    } finally {
      setIsLoading(false);
    }
  };

  // Fetch system context only in useEffect
  useEffect(() => {
    if (projectId) {
      getSystemContext();
    }
  }, [projectId, searchParams.toString(), type, overviewFlag, containersFlag]);


  useEffect(() => {
    if (configStatus[currentTaskId]) {

      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setAutoNavigateEnabled(false)

    }
  }, [currentTaskId, configStatus[currentTaskId], projectId])

  useEffect(() => {
    const fetchSystemContextOnComplete = async () => {
      if (taskStatus.toLowerCase() === "complete" && projectId) {
        await getSystemContext();
      }
    };

    fetchSystemContextOnComplete();
  }, [taskStatus, projectId])

  const isDiscussionCompleted = useCallback((discussionType) => {
    if (!systemContext?.data?.systemContext?.properties) return false;

    const configKey = CONFIG_STATE_MAPPING[discussionType];
    return systemContext.data.systemContext.properties[configKey] === "configured";
  }, [systemContext]);


  useEffect(() => {
    if (systemContext?.data?.systemContext?.properties) {

      const overviewConfigured = systemContext.data.systemContext.properties.overview_config_state === "configured";
      const containersConfigured = systemContext.data.systemContext.properties.containers_config_state === "configured";

      if (overviewConfigured && !containersConfigured) {
        // First discussion completed but second is not - show second discussion
        setCurrentDiscussionIndex(1);
      } else if (!overviewConfigured) {
        // First discussion not completed - show first discussion
        setCurrentDiscussionIndex(0);
      }
    }
  }, [systemContext]);

  const LoaderComponent = () => {

    return (
      <div className="flex gap-4 p-4">


        <div className="p-4 space-y-6 flex-1">
          <div className="border border-gray-200 flex p-4 flex-col space-y-3">
            <div className="h-6 bg-gray-100 animate-pulse rounded-lg w-2/3"></div>
            <div className="flex items-center justify-between">
              <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/3"></div>
              <div className="h-8 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
            </div>
          </div>

          <div className="border border-gray-200 rounded-lg p-4 space-y-3">
            <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
            <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
            <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
            <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
          </div>

          <div className="border border-gray-200 rounded-lg p-4 space-y-3">
            <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
            <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
            <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
            <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
          </div>


        </div>
      </div>
    );

  }

  const LoadingSpinner = () => (
    <div className="w-full animate-pulse rounded-lg overflow-hidden border border-gray-200">
      {/* Table header */}
      <div className="flex items-center p-4 border-b bg-gray-50 rounded-t-lg">
        <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
        <div className="flex-1 grid grid-cols-5 gap-4">
          <div className="h-6 bg-gray-200 rounded col-span-2"></div>
          <div className="h-6 bg-gray-200 rounded"></div>
          <div className="h-6 bg-gray-200 rounded"></div>
          <div className="h-6 bg-gray-200 rounded"></div>
        </div>
      </div>

      {/* Table rows */}
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-center p-4 border-b hover:bg-gray-50">
          <div className="w-5 h-5 bg-gray-200 rounded mr-3"></div>
          <div className="flex-1 grid grid-cols-5 gap-4">
            <div className="col-span-2">
              <div className="h-5 bg-gray-200 rounded mb-2 w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div className="flex items-center">
              <div className="h-6 w-16 bg-gray-200 rounded"></div>
            </div>
            <div className="h-5 bg-gray-200 rounded w-1/2"></div>
            <div className="h-5 bg-gray-200 rounded w-1/4"></div>
          </div>
        </div>
      ))}

      {/* Table footer / pagination */}
      <div className="flex justify-between items-center p-4 bg-gray-50 rounded-b-lg">
        <div className="h-8 w-32 bg-gray-200 rounded"></div>
        <div className="flex space-x-2">
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );

  const handleBackToContainers = () => {
    setShowContainerDetails(false);
    setSelectedContainerId(null);
  };

  useEffect(() => {
    const generateCodeParam = searchParams.get("GenerateCode");
    const containerId = searchParams.get("containerid");
    const stepName = searchParams.get("stepName")

    if (generateCodeParam === "true" && containerId && stepName === "Containers") {
      onGenerateCode(parseInt(containerId));
    }
  }, [searchParams]);

  const onGenerateCode = (id) => {
    // For containers tab, use the selected container info

    if (type === "container" && systemContext?.data?.containers) {

      const selectedContainer = systemContext.data.containers.find(c => c.id === id);

      if (selectedContainer) {
        setSelectedSystemContext(selectedContainer);
        setSelectedContainerId(id);
        // Set platform and framework from container if available
        if (selectedContainer.properties.platform) {

          const platformData = PLATFORMS.find(p => p.key === selectedContainer.properties.platform);
          setCurrentPlatform(platformData ? {
            key: platformData.key,

            label: platformData.label,

            icon: platformData.icon

          } : { key: "generic", label: "Generic", icon: <Generic /> });

        } else {
          setCurrentPlatform({ key: "generic", label: "Generic", icon: <Generic /> });
        }
        // Set framework if available in container properties

        if (selectedContainer.properties.framework) {
          const frameworkData = frameworks.find(f => f.key === selectedContainer.properties.framework) ||

            backendFrameworks.find(f => f.key === selectedContainer.properties.framework) ||

            mobileFrameworks.find(f => f.key === selectedContainer.properties.framework);

          if (frameworkData) {
            setCurrentFramework(frameworkData);
          } else {
            setCurrentFramework(frameworks[0]);
          }
        } else {
          setCurrentFramework(frameworks[0]);
        }
      }

    } else {
      // For system context tab, use the system context data
      if (systemContext && systemContext.data) {
        setSelectedSystemContext(systemContext.data.systemContext);
        setSelectedContainerId(systemContext.data.systemContext.id);

        // Set default platform and framework
        setCurrentPlatform({ key: "generic", label: "Generic", icon: <Generic /> });
        setCurrentFramework(frameworks[0]);
      } else {
        // Handle case when systemContext or systemContext.data is null
        setCurrentPlatform({ key: "generic", label: "Generic", icon: <Generic /> });
        setCurrentFramework(frameworks[0]);
      }
    }
    // Fetch repository details for this container/system context
    fetchRepositoryDetails(id);
    // Open the code generation setup modal
    setCodeGenSetupModal(true);
  };

  const fetchRepositoryDetails = async (id) => {
    try {

      setRepositoryState({
        state: 'loading',
        data: null

      });

      const [response, allBranches] = await Promise.all([
        getRepository(projectId, id),
        fetchAllBranchesForContainer(id) // Fetch ALL branches
      ]);

      if (response.repository) {

        setRepositoryState({
          state: 'success',
          data: response.repository

        });
        setIsRepoConfigured(true);
      } else {

        setRepositoryState({
          state: 'error',
          data: null

        });
        setIsRepoConfigured(false);
      }
    } catch (err) {

      setRepositoryState({
        state: 'error',
        data: null

      });
      setIsRepoConfigured(false);

    }

  };

const handlePropertyUpdate = async (key, value) => {
  try {
    const response = await updateNodeByPriority(selectedContainerId, key, value);

    if (response === "success" || response?.status === "success") {
      // Update local state
      if (type === "container") {
        setSelectedSystemContext((prev) => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value,
          },
        }));

        setSystemContext((prevState) => {
          const updatedContainers = prevState.data.containers.map((container) => {
            if (container.id === selectedContainerId) {
              return {
                ...container,
                properties: {
                  ...container.properties,
                  [key]: value,
                },
              };
            }
            return container;
          });

          return {
            ...prevState,
            data: {
              ...prevState.data,
              containers: updatedContainers,
            },
          };
        });
      } else {
        setSelectedSystemContext((prev) => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value,
          },
        }));

        setSystemContext((prevState) => ({
          ...prevState,
          data: {
            ...prevState.data,
            systemContext: {
              ...prevState.data.systemContext,
              properties: {
                ...prevState.data.systemContext.properties,
                [key]: value,
              },
            },
          },
        }));
      }


      showAlert(`${key.charAt(0).toUpperCase() + key.slice(1)} updated successfully`, "success");
    } else {
      throw new Error("Update failed");
    }
  } catch (error) {
   
    showAlert("Failed to update content", "error");
  }
};


  const handleGenerateCode = () => {
    setIsGeneratingCode(true);
  };

  const handleCloseCodeGenModel = () => {
    setCodeGenSetupModal(false)
    setIsChecked(false)
    const newParams = new URLSearchParams(searchParams);
    if (newParams.has("containerid")) {
      newParams.delete("containerid");
    }
    if (newParams.has("GenerateCode")) {
      newParams.delete("GenerateCode");
    }
    router.replace(`${pathname}?${newParams.toString()}`, { scroll: false })

  }

  const handlePlatformChange = (platformData) => {

    setCurrentPlatform(platformData);

    handlePropertyUpdate("platform", platformData.key);
    // Update framework based on platform change

    if (platformData.key === "mobile") {

      setCurrentFramework(mobileFrameworks[0]);

      handlePropertyUpdate("framework", mobileFrameworks[0].key);

    } else if (platformData.key === "web") {

      setCurrentFramework(frameworks[0]);

      handlePropertyUpdate("framework", frameworks[0].key);

    } else if (platformData.key === "backend") {
      setCurrentFramework(backendFrameworks[0]);
      handlePropertyUpdate("framework", backendFrameworks[0].key);
    } else {
      setCurrentFramework(frameworks[0]);
      handlePropertyUpdate("framework", frameworks[0].key);
    }
  };

  const handleFrameworkChange = (newFramework) => {
    setCurrentFramework(newFramework);
    handlePropertyUpdate("framework", newFramework.key);
  };

  const handleBranchUpdate = async (newBranch) => {
    try {
      await handlePropertyUpdate("branch", newBranch);
    } catch (error) {
      showAlert('Failed to update branch', 'error');

    }

  };
  const BranchSelection = () => {
    // Allow branch selection even if selectedContainerId is not set initially
    if (!selectedContainerId) {
      console.warn('BranchSelection rendered without selectedContainerId');

    }

    const containerKey = `container_${selectedContainerId}`;
    const containerData = containerBranches[containerKey] || {
      branches: [],
      isFetching: false,
      error: false,
      pagination: { currentPage: 1, totalPages: 1, perPage: 30, totalCount: 0 }
    };

    // Function to handle branch fetching for pagination
    const handleFetchBranches = async (page = 1) => {

      return getPaginatedBranches(selectedContainerId, page);
    };

    // Function to force complete refresh of branches
    const handleForceRefreshBranches = async (newBranchName = null) => {
      if (selectedContainerId) {
        await fetchAllBranchesForContainer(selectedContainerId, async () => {
          // This callback will be called after branches are fetched
          // Wait a moment to ensure the branch list is updated
          await new Promise(resolve => setTimeout(resolve, 300));

          // If a new branch name is provided, update the property
          if (newBranchName) {
            await handlePropertyUpdate("branch", newBranchName);
          }
        });
      }
    };


    const { branches: paginatedBranches, pagination } = getPaginatedBranches(
      selectedContainerId,
      containerData.pagination?.currentPage || 1
    );


    const currentContainer = systemContext?.data?.containers?.find(c => c.id === selectedContainerId);
    const currentBranch = currentContainer?.properties?.branch || selectedSystemContext?.properties?.branch;

    return (
      <BranchSelector
        key={`branch-selector-${selectedContainerId || 'default'}-${currentBranch || 'none'}`}
        projectId={projectId}
        containerId={selectedContainerId}
        currentBranch={currentBranch}
        onUpdate={handleBranchUpdate}
        className="w-full"
        branches={paginatedBranches}
        pagination={pagination}
        onFetchBranches={handleFetchBranches}
        isFetchingBranches={containerData.isFetching}
        repoError={containerData.error}
        onForceRefreshBranches={handleForceRefreshBranches}
      />
    );
  };

  const handleRepoDetailsOpen = () => {
    setShowRepoDetails(true);
  };

  const handleRepoDetailsClose = (success = false) => {
    setShowRepoDetails(false);
    if (success) {
      setIsRepoConfigured(true);
      showAlert('Repository configured successfully', 'success');
      fetchRepositoryDetails(selectedContainerId); // Refresh repository data
    }
  };
  const handleSystemContextUpdate = async (key, value) => {
    try {
       const response = await updateNodeByPriority(systemContext?.data?.systemContext?.id, key, value);
      const updatedProps = {
        ...systemContext.data.systemContext.properties,
        [key]: value,
      };

      setSystemContext((prev) => ({
        ...prev,
        data: {
          ...prev.data,
          systemContext: {
            ...prev.data.systemContext,
            properties: updatedProps,
          },
        },
      }));

      showAlert("Content updated successfully", "success");
    } catch (error) {
      showAlert("Failed to update content", "error");
    }
  };

  // Handle row selection 

  const handleCheckboxChange = (id, isChecked) => {
    if (isChecked) {
      setSelectedContainerIdVal(id)
      setIsChecked(isChecked)
    } else {
      setIsChecked(isChecked)
    }
  };



  useEffect(() => {

    if (searchParams.get("task_id")) {
      setCodeGenSetupModal(false);
      setIsGeneratingCode(false);

    }
  }, [searchParams]);


  if (isLoading && type === "overview") {
    return <LoaderComponent />;
  }

  if (isLoading && type === "container" && !showContainerDetails) {
    return <LoadingSpinner />;
  }

  const headers = [
    { key: "isProjectGuidance-checkbox", lablel: "" },
    { key: "id", label: "Id" },
    { key: "title", label: "Title" },
    { key: "container_type", label: "Container Type" },
    // { key: "type", label: "Type" },
    { key: "description", label: "Description" },
  ];

  // Check configuration status based on type
  const isConfigured =
    type === "overview"
      ? systemContext?.data?.systemContext?.properties?.overview_config_state ===
      "configured" && systemContext?.data?.systemContext?.properties?.containers_config_state === "configured"
      // : type === "container"
      //   ? systemContext?.data?.systemContext?.properties?.containers_config_state === "configured"
      : systemContext?.data?.containers &&
      systemContext.data.containers.length > 0;

  const hasContainers =
    systemContext?.data?.containers && systemContext.data.containers.length > 0;

  // Determine what to render based on type and configuration status
  const renderContent = () => {
    // For container type
    if (type === "container") {
      if (isConfigured) {
        // If we're showing container details, render ContainerConfigurationStep
        if (showContainerDetails && selectedContainerId) {
          return (

            <div className="h-full">
              <div className="mb-4">

              </div>
              <ContainerConfigurationStep handleBackToContainers={handleBackToContainers} />

            </div>

          );

        }
        // Show ONLY containers table if containers exist
        return (
          <div className="">

            {/* Generate Code button - only show when rows are selected */}
            {/* 
            {isChecked&& (

              <div className="mb-2 flex justify-end ">

                <DynamicButton
                  type="submit"
                  size="medium"
                  icon={CodeXml}
                  variant="primaryLegacy"
                  label="Generate Code"
                  onClick={() => onGenerateCode(selectedContainerIdVal)}
                  text="Generate Code"
                  className="bg-primary-50 text-primary-600 hover:bg-primary-100"
                  tooltip="Generate the code"
                />
              </div>

            )} */}
            <div id="system-containers" className="relatedComponentDiv">
              <TableComponent
                data={systemContext.data.containers
                  .map((data) => ({
                    id: data.id,
                    title: data.properties.Title,
                    container_type: data.properties.ContainerType,
                    description: cleanDescription(data.properties.Description),
                  }))
                  // Sort containers - internal first, then external
                  .sort((a, b) => {

                    if (a.container_type?.toLowerCase() === "internal" && b.container_type?.toLowerCase() !== "internal") {
                      return -1;
                    }
                    if (b.container_type?.toLowerCase() === "internal" && a.container_type?.toLowerCase() !== "internal") {
                      return 1;
                    }
                    return 0;
                  })}
                onRowClick={(id) => {
                  // Set the selected container ID and show container details
                  setSelectedContainerId(id);
                  setShowContainerDetails(true);
                  if (setContainerId) {
                    setContainerId(id);
                  }
                  // Navigate to the Container Configuration step
                  const newSearchParams = new URLSearchParams(searchParams);
                  if (newSearchParams.has("containerid")) {
                    newSearchParams.delete("containerid");
                  }
                  if (newSearchParams.has("GenerateCode")) {
                    newSearchParams.delete("GenerateCode");
                  }
                  newSearchParams.set("stepName", "Container Details");
                  newSearchParams.set("selectedContainerId", id);
                  router.replace(`${pathname}?${newSearchParams.toString()}`, { scroll: false });
                }}
                headers={headers}
                sortableColumns={{
                  id: true,
                  title: true,
                  container_type: true,
                  type: true,
                }}
                itemsPerPage={20}
                title={en.ChildContainersHeading}
                onCheckboxChange={handleCheckboxChange}
              />
            </div>
          </div>
        );
      } else {
        // Show configuration buttons if containers don't exist
        return renderConfigButtons();
      }
    }
    // For overview type
    else {
      if (isConfigured) {
        // Show ONLY properties for overview type
        return (
          <div>
            {systemContext.data.systemContext.properties?.Title && (
              <>
                <div className="typography-body-lg font-weight-medium text-gray-800">
                  Title: {systemContext.data.systemContext.properties.Title}
                </div>

                <PropertiesRenderer
                  properties={systemContext.data.systemContext.properties}
                  metadata={systemContext.model?.SystemContext?.ui_metadata}
                  to_skip={["Type", "Title", "configuration_state"]}
                  onUpdate={handleSystemContextUpdate}
                />
              </>
            )}
          </div>
        );
      } else {
        // Show configuration buttons if system context isn't configured
        return renderConfigButtons();
      }
    }
  };

  // Helper function to render configuration buttons
  const renderConfigButtons = () => {
    return (
      <div className="flex-1 p-4 flex flex-col">
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="typography-body-lg font-weight-medium text-gray-700 mb-4">
              Configure {getDiscussionTitle(currentDiscussionType)}
            </h3>

            {/* Progress Bar aligned right */}
            <div className="flex items-center space-x-2">
              <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                Discussion Progress
              </span>
              <span className="text-primary-700 typography-body-sm">
                {currentDiscussionIndex + 1} / {DISCUSSION_TYPES.length}
              </span>
              <div className="w-24 h-2 bg-primary-100 rounded-full">
                <div
                  className="h-2 bg-primary rounded-full transition-all duration-300"
                  style={{
                    width: `${((currentDiscussionIndex + 1) / DISCUSSION_TYPES.length) * 100}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>

          <div className="mt-4 p-4 bg-amber-100 border-l-4 border-amber-400 rounded-md flex items-center">
            <AlertCircle className="text-amber-600 mr-3" size={24} />
            <p className="text-amber-800 typography-body-sm font-weight-medium">
              To view  system context details, please complete both discussions
            </p>
          </div>
        </div>

        {/* Discussion Status Pills */}
        <div className="flex flex-wrap gap-2 mb-6">
          {DISCUSSION_TYPES.map((type, index) => {
            const isCompleted = isDiscussionCompleted(type);
            // First discussion is always selectable, second only if first is completed
            const isSelectable = index === 0 || isDiscussionCompleted(DISCUSSION_TYPES[0]);

            return (
              <button
                key={type}
                onClick={() => {
                  if (isSelectable && !isCompleted) {
                    setCurrentDiscussionIndex(index);
                  }
                }}
                className={`px-3 py-1 rounded-full typography-body-sm flex items-center gap-1 
                    ${index === currentDiscussionIndex
                    ? 'bg-primary-100 text-primary-800 border border-primary-300'
                    : isCompleted
                      ? 'bg-green-100 text-green-800'
                      : isSelectable
                        ? 'bg-gray-100 text-gray-600'
                        : 'bg-gray-100 text-gray-400 opacity-60 cursor-not-allowed'
                  }`}
                disabled={isCompleted || (index !== 0 && !isDiscussionCompleted(DISCUSSION_TYPES[0]))}
              >
                {index + 1}. {getDiscussionTitle(type)}
                {isCompleted && (
                  <CheckCircle size={14} className="text-green-600" />
                )}
              </button>
            );
          })}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
          {currentDiscussionType === "system_context_overview" ? (
            <div className="col-span-1 md:col-span-1 lg:col-span-2">
              <div
                className={`border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === "discussion"
                  ? "border-primary bg-primary-50"
                  : "border-gray-200"
                  }`}
                onClick={() => {
                  setConfigMethod("discussion");
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.set("discussion", "new");
                  newSearchParams.set("node_id", systemContext?.data?.systemContext?.id);
                  newSearchParams.set("node_type", "SystemContext");
                  newSearchParams.set("discussionType", "system_context_overview");
                  newSearchParams.set("is_creating_system_context", "true");
                  router.push(`${buildProjectUrl(projectId.toString(), 'architecture/system-context')}?${newSearchParams.toString()}`);
                }}
              >
                <div className="flex items-center space-x-2 mb-4">
                  <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Image
                      src={Logo}
                      alt="Logo"
                      width={16}
                      height={16}
                      className="text-primary"
                    />
                  </div>
                  <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
                </div>

                <p className="text-gray-600">
                  {en.SystemContextUpdate}
                </p>

                {isDiscussionCompleted("system_context_overview") && (
                  <div className="mt-4 flex items-center text-green-700">
                    <CheckCircle size={16} className="mr-1" />
                    Completed
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="col-span-1 md:col-span-1 lg:col-span-2">
              <div
                className={`border rounded-lg p-6 transition-all hover:shadow-md cursor-pointer 
                    ${configMethod === 'discussion' ? 'border-primary bg-primary-50' : 'border-gray-200'}
                    ${!isDiscussionCompleted("system_context_overview") ? 'opacity-75 cursor-not-allowed pointer-events-none' : ''}
                  `}
                onClick={() => {
                  if (isDiscussionCompleted("system_context_overview")) {
                    setConfigMethod("discussion");
                    const newSearchParams = new URLSearchParams(searchParams);
                    newSearchParams.set("discussion", "new");
                    newSearchParams.set("node_id", systemContext?.data?.systemContext?.id);
                    newSearchParams.set("node_type", "SystemContext");
                    newSearchParams.set("discussionType", "system_context_containers");
                    newSearchParams.set("is_creating_system_context", "true");
                    router.push(`${buildProjectUrl(projectId.toString(), 'architecture/system-context')}?${newSearchParams.toString()}`);
                  }
                }}
                title={
                  !isDiscussionCompleted("system_context_overview")
                    ? "Please complete System Context Overview before configuring containers."
                    : ""
                }
              >
                <div className="flex items-center space-x-2 mb-4">
                  <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Image
                      src={Logo}
                      alt="Logo"
                      width={16}
                      height={16}
                      className="text-primary"
                    />
                  </div>
                  <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
                </div>

                <p className="text-gray-600">
                  {en.SystemContextUpdate}
                </p>

                {isDiscussionCompleted("system_context_containers") && (
                  <div className="mt-4 flex items-center text-green-700">
                    <CheckCircle size={16} className="mr-1" />
                    Completed
                  </div>
                )}
              </div>
            </div>
          )}

          <div
            className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md${configMethod === "auto"
              ? "border-primary bg-primary-50"
              : "border-gray-200"
              }`}
            onClick={handleConfigureClick}
          >
            <div className="flex mb-4 items-center space-x-2">
              <div className="p-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                <Upload className="w-4 h-4 text-primary" />
              </div>
              <h4 className="typography-body-lg font-weight-medium">Auto Configuration</h4>
            </div>

            <p className="text-gray-600">
              Let our LLM automatically configure this {currentDiscussionType === "system_context_overview" ? "System Context" : "Container"} based on the available information
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full  overflow-y-auto px-4">
      {showConfigModel && (taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle') ? (
        <StatusPanel />
      ) : (
        <>
          {configureModel && (
            <ConfigureModal
              id={projectId}
              type={"Architecture"}
              isNodeType={"Architecture"}
              isCreateProject={true}
              setShowConfigModel={setShowConfigModel}
              closeModal={handleCloseModal}
              setLoadingAutoConfigure={setLoadingAutoConfigure}
              onSubmitSuccess={() => {
                showAlert(
                  `Architecture configuration initiated successfully`,
                  "success"
                );
                setIsVertCollapse(false);

                // Log successful configuration to MongoDB based on type
                const stepName =
                  type === "overview"
                    ? "system_context_overview"
                    : "system_context_containers";

                createProjectGuidanceFlow(parseInt(projectId), {
                  project_id: parseInt(projectId),
                  step_name: stepName,
                  status: "completed",
                  data: {
                    system_context_id: systemContext?.data?.systemContext
                      ? parseInt(systemContext.data.systemContext.id)
                      : null,
                    type: "SystemContext",
                    status: "configured",
                  },
                })
                  .then((result) => {


                    // Set the appropriate flag based on type
                    if (type === "overview") {
                      sessionStorage.setItem(
                        `openSystemContextOverview-${projectId}`,
                        "true"
                      );
                    } else {
                      sessionStorage.setItem(
                        `openSystemContextContainers-${projectId}`,
                        "true"
                      );
                    }
                  })
                  .catch((error) => {

                  });
              }}
            />
          )}
          {/* Code Generation Setup Modal */}

          {codeGenSetupModal && (
            <CodeGenerationSetupModal
              onClose={handleCloseCodeGenModel}
              onConfigureRepo={handleRepoDetailsOpen}
              BranchSelection={BranchSelection}
              currentPlatform={currentPlatform}
              onPlatformChange={handlePlatformChange}
              onConfirm={handleGenerateCode}
              repository={repositoryState.data}
              currentBranch={selectedSystemContext?.properties?.branch}
              currentFramework={currentFramework}
              onFrameworkChange={handleFrameworkChange}
              isGeneratingCode={isGeneratingCode}
              isModal={true}
              projectId={projectId}
              containerId={parseInt(selectedContainerId)}
              handleRepoChange={(repo) => {

                setRepositoryState({
                  state: 'success',
                  data: repo
                });
                showAlert('Repository configured successfully', 'success');
                // Close the repository modal if it's open
                setShowRepoDetails(false);
              }}
            />
          )}

          {/* Repository Modal */}

          {showRepoDetails && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-4 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <RepositoryDetailsModal
                  open={true}
                  projectId={projectId}
                  containerId={selectedContainerId}
                  onClose={handleRepoDetailsClose}
                  onSuccess={() => handleRepoDetailsClose(true)}

                />
              </div>
            </div>
          )}

          {/* Code Generation Handler */}

          {isGeneratingCode && (
            <CodeGenerationHandler
              projectId={projectId}
              itemId={selectedContainerId}
              onComplete={() => {
                setIsGeneratingCode(false);
              }}
            />

          )}

          {/* Code Generation Modal */}
          {isVisible && <CodeGenerationModal />}

          {renderContent()}
        </>)}
    </div>
  );
}
