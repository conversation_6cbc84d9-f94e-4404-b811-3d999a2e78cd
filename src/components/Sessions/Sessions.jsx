import React, { useState, useEffect, useRef, useContext, useCallback } from 'react';
import { Search, Code, FileEdit, X, Loader2, Clock, ChevronDown, ChevronUp, Play, RefreshCw } from 'lucide-react';
import { useSearchParams, usePathname, useRouter, useParams } from 'next/navigation';
import { useCodeGeneration } from '../Context/CodeGenerationContext';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { useUser } from '../Context/UserContext';
import { resumeStartCodeGeneration, resumeStartCodeMaintenance } from '@/utils/api';
import { getSessionPodStatus } from '@/utils/batchAPI';
import { AlertContext } from '../NotificationAlertService/AlertList';

const TableSkeleton = () => (
  <>
    {[...Array(5)].map((_, idx) => (
      <tr key={idx} className="border-b border-gray-100 animate-pulse">
        {/* Type Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="flex items-center justify-center gap-3">
            <div className="p-2 bg-gray-100 rounded-lg">
              <div className="w-[18px] h-[18px] bg-gray-200 rounded"></div>
            </div>
            <div className="w-20 h-6 bg-gray-200 rounded-full"></div>
          </div>
        </td>

        {/* Session Column */}
        <td className="px-6 py-4 text-center">
          <div className="flex flex-col items-center">
            <div className="w-40 h-4 bg-gray-200 rounded mb-1"></div>
            <div className="w-24 h-3 bg-gray-100 rounded"></div>
          </div>
        </td>

        {/* Status Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="w-16 h-6 bg-gray-200 rounded-full mx-auto"></div>
        </td>

        {/* Date Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="w-20 h-4 bg-gray-200 rounded mb-1 mx-auto"></div>
          <div className="w-16 h-3 bg-gray-100 rounded mx-auto"></div>
        </td>

        {/* Duration Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="flex items-center justify-center">
            <div className="w-3.5 h-3.5 bg-gray-200 rounded mr-1.5"></div>
            <div className="w-8 h-4 bg-gray-200 rounded"></div>
          </div>
        </td>

        {/* Actions Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="flex items-center justify-center gap-2">
            <div className="w-[120px] h-8 bg-gray-200 rounded-lg"></div>
            <div className="w-[120px] h-8 bg-gray-200 rounded-lg"></div>
          </div>
        </td>
      </tr>
    ))}
  </>
);

const Sessions = ({ initialSessions = [], isLoading = false, onFilterChange, onCloseModal, onRefresh }) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const { is_having_permission } = useUser();
  const { showAlert } = useContext(AlertContext);

  // State management
  const [sessions, setSessions] = useState(initialSessions);
  const [allSessions, setAllSessions] = useState(initialSessions);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('All types');
  const [statusFilter, setStatusFilter] = useState('All statuses');
  const [dateFilter, setDateFilter] = useState(null);
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const { isVisible, setIsVisible, setCurrentIframeUrl, setLlmModel } = useCodeGeneration();
  const [resumingSession, setResumingSession] = useState(null);
  const [processedRequests, setProcessedRequests] = useState(new Set());
  
  // Use ref for immediate blocking of duplicate requests
  const processingRequests = useRef(new Set());

  // Add debounce timer ref
  const debounceTimerRef = useRef(null);

  const { projectId } = useParams();



  // Update sessions when props change
  useEffect(() => {
    setSessions(initialSessions);
    setAllSessions(initialSessions);
  }, [initialSessions]);

  // Auto-apply filters whenever filter values change
  useEffect(() => {
    applyFilters();
  }, [typeFilter, statusFilter, searchQuery, dateFilter, sortField, sortDirection]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Handle search input change with debouncing
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
  };

  // Apply filters and sorting
  const applyFilters = useCallback(() => {
    const selectedType = typeFilter.trim().toLowerCase();
    const selectedStatus = statusFilter.trim().toLowerCase();
    const keyword = searchQuery.trim().toLowerCase();

    const selectedDate = dateFilter && dayjs(dateFilter).isValid()
      ? dayjs(dateFilter).format('YYYY-MM-DD')
      : null;

    let filtered = allSessions.filter((session) => {
      const sessionType = session.icon?.toLowerCase();
      const sessionStatus = session.status?.toLowerCase();
      const sessionTitle = (session.title || '').toLowerCase();

      const rawDate = session.date?.split('•')[0]?.trim();
      const parsedDate = dayjs(rawDate, ['MMM D, YYYY', 'MMMM D, YYYY'], true);
      const sessionDate = parsedDate.isValid() ? parsedDate.format('YYYY-MM-DD') : null;

      const matchesType =
        selectedType === 'all types' ||
        (selectedType === 'generation' && sessionType === 'code') ||
        (selectedType === 'maintenance' && sessionType === 'edit');

      const matchesStatus =
        selectedStatus === 'all statuses' || 
        sessionStatus === selectedStatus ||
        (selectedStatus === 'running' && (sessionStatus === 'running' || sessionStatus === 'in_progress' || sessionStatus === 'in progress')) ||
        (selectedStatus === 'completed' && (sessionStatus === 'complete' || sessionStatus === 'completed')) ||
        (selectedStatus === 'stopped' && sessionStatus === 'stopped');

      const matchesSearch = !keyword || sessionTitle.includes(keyword);

      const matchesDate = !selectedDate || sessionDate === selectedDate;

      return matchesType && matchesStatus && matchesSearch && matchesDate;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortField) {
        case 'title':
          aValue = a.title?.toLowerCase() || '';
          bValue = b.title?.toLowerCase() || '';
          break;
        case 'status':
          aValue = a.status?.toLowerCase() || '';
          bValue = b.status?.toLowerCase() || '';
          break;
        case 'type':
          aValue = a.icon?.toLowerCase() || '';
          bValue = b.icon?.toLowerCase() || '';
          break;
        case 'date':
        default:
          aValue = dayjs(a.date?.split('•')[0]?.trim(), ['MMM D, YYYY', 'MMMM D, YYYY']).valueOf() || 0;
          bValue = dayjs(b.date?.split('•')[0]?.trim(), ['MMM D, YYYY', 'MMMM D, YYYY']).valueOf() || 0;
          break;
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setSessions(filtered);
  }, [allSessions, typeFilter, statusFilter, searchQuery, dateFilter, sortField, sortDirection]);

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get icon based on type
  const getIcon = (iconType) => {
    switch (iconType) {
      case 'code':
        return <Code size={18} className="text-green-600" />;
      case 'edit':
        return <FileEdit size={18} className="text-purple-600" />;
      default:
        return <Code size={18} className="text-gray-600" />;
    }
  };

  // Get type badge style
  const getTypeBadgeStyle = (iconType) => {
    switch (iconType) {
      case 'code':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'edit':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };



  // Get type label
  const getTypeLabel = (iconType) => {
    switch (iconType) {
      case 'code':
        return 'Generation';
      case 'edit':
        return 'Maintenance';
      default:
        return 'Unknown';
    }
  };

  // Get status style
  const getStatusStyle = (status) => {
    switch (status?.toLowerCase()) {
      case 'running':
      case 'in_progress':
      case 'in progress':
        return 'bg-primary-50 text-primary-700 border-primary-200';
      case 'complete':
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'submitted':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'stopped':
        return 'bg-gray-50 text-gray-700 border-gray-200';
      case 'failed':
        return 'bg-red-50 text-red-700 border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  // Format status display with proper capitalization
  const formatStatusDisplay = (status) => {
    if (!status) return 'Unknown';
    
    const statusLower = status.toLowerCase();
    
    // Handle special cases
    if (statusLower === 'in_progress' || statusLower === 'in progress' || statusLower === 'running') {
      return 'Running';
    }
    
    if (statusLower === 'complete' || statusLower === 'completed') {
      return 'Completed';
    }
    
    if (statusLower === 'stopped') {
      return 'Stopped';
    }
    
    if (statusLower === 'submitted') {
      return 'Submitted';
    }
    
    if (statusLower === 'failed') {
      return 'Failed';
    }
    
    // Capitalize first letter for all other statuses
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  // Handle resume functionality
  const handleResumeClick = async (session) => {
    if (!session.id) {
      showAlert("Session ID not found", "error");
      return;
    }

    // Immediate blocking using ref to prevent race conditions
    if (processingRequests.current.has(session.id)) {
      return;
    }

    // Also check state-based tracking
    if (processedRequests.has(session.id)) {
      return;
    }

    // Add to both immediate ref and state tracking
    processingRequests.current.add(session.id);
    setProcessedRequests(prev => new Set([...prev, session.id]));
    setResumingSession(session.id);

    try {
      // First check session pod status
      showAlert("Checking session status...", "info");
      const podStatus = await getSessionPodStatus(session.id);
      console.log('Session pod status:', podStatus);
      
      // If session is already running, redirect to task_id
      if (podStatus.is_running) {
        showAlert("Session is already running, redirecting...", "success");
        
        // Redirect to existing task
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("task_id", session.id);
        const newUrl = `${pathname}?${newSearchParams.toString()}`;
        
        // Update URL without navigation to keep modal open
        window.history.pushState({}, '', newUrl);
        
        // Refresh sessions to show updated status
        if (onRefresh) {
          onRefresh();
        }
        return;
      }

      // If not running, proceed with resume
      let response;
      
      if (session.icon === 'edit') {
        const selectedRepos = { all_repositories: true };
        const sessionName = "Untitled";
        const sessionDescription = "Resumed maintenance session";
        
        response = await resumeStartCodeMaintenance(
          projectId,
          session.id,
          selectedRepos,
          sessionName,
          sessionDescription
        );
      } else {
        const architectureId = session.architecture_id || null;
        let containerIds = [];
        
        if (session.container_ids && Array.isArray(session.container_ids)) {
          const flattenDeep = (arr) => {
            return arr.reduce((acc, val) => {
              if (Array.isArray(val)) {
                return acc.concat(flattenDeep(val));
              } else {
                const num = parseInt(val);
                if (!isNaN(num)) {
                  acc.push(num);
                }
                return acc;
              }
            }, []);
          };
          
          containerIds = flattenDeep(session.container_ids);
        } else if (session.container_id) {
          const containerId = parseInt(session.container_id);
          if (!isNaN(containerId)) {
            containerIds = [containerId];
          }
        }

        response = await resumeStartCodeGeneration(
          projectId,
          architectureId,
          session.id,
          containerIds
        );
      }

      if (response && response.task_id) {
        showAlert("Session resumed successfully", "success");
        
        // Keep modal open and update URL parameters
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("task_id", response.task_id);
        const newUrl = `${pathname}?${newSearchParams.toString()}`;
        
        // Update URL without navigation to keep modal open
        window.history.pushState({}, '', newUrl);
        
        // Refresh sessions to show updated status
        if (onRefresh) {
          onRefresh();
        }
      } else if (response && response.error) {
        showAlert(response.error, "error");
      } else {
        showAlert("Failed to resume session. Please try again.", "error");
      }
    } catch (error) {
      console.error(`Failed to resume ${session.icon === 'edit' ? 'code maintenance' : 'code generation'}:`, error);
      
      let errorMessage = `Failed to resume ${session.icon === 'edit' ? 'code maintenance' : 'code generation'}. Please try again.`;
      
      if (error.message) {
        errorMessage = error.message;
      }

      showAlert(errorMessage, "error");
    } finally {
      setResumingSession(null);
      
      // Clean up both ref and state tracking
      processingRequests.current.delete(session.id);
      setProcessedRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(session.id);
        return newSet;
      });
    }
  };

  // Add refresh handler
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      if (onRefresh) {
        await onRefresh();
      }
    } catch (error) {
      console.error('Error refreshing sessions:', error);
      showAlert("Failed to refresh sessions", "error");
    } finally {
      setIsRefreshing(false);
    }
  };



  // Render table header with sorting
  const renderTableHeader = (field, label) => (
    <th 
      className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer hover:bg-gray-50 transition-colors duration-200"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center justify-center gap-2">
        <span>{label}</span>
        {sortField === field && (
          sortDirection === 'asc' ? 
            <ChevronUp size={14} className="text-gray-400" /> : 
            <ChevronDown size={14} className="text-gray-400" />
        )}
      </div>
    </th>
  );

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header and Filters */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-8 py-6">
          {/* Title Section */}
          <div className="mb-8 flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Filter Sessions</h1>
              <div className="w-16 h-1 bg-red-500 rounded-full"></div>
            </div>
            <button
              onClick={onCloseModal}
              className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
              aria-label="Close modal"
            >
              <X className="w-5 h-5 text-gray-500 hover:text-gray-700" />
            </button>
          </div>

          {/* Filter Section */}
          <div className="rounded-lg p-4 border border-gray-100 max-w-full">
            <div className="flex items-center gap-3">
              
              {/* Search */}
              <div className="relative w-[280px]">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search by session name or ID..."
                  className="block w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary bg-white transition-all duration-200 hover:border-gray-400"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>

              {/* Type Filter */}
              <div className="relative w-[130px]">
                <select
                  className="w-full pl-4 pr-10 py-2.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary bg-white appearance-none transition-all duration-200 hover:border-gray-400"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <option>All types</option>
                  <option>Generation</option>
                  <option>Maintenance</option>
                </select>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
              </div>

              {/* Status Filter */}
              <div className="relative w-[130px]">
                <select
                  className="w-full pl-4 pr-10 py-2.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary bg-white appearance-none transition-all duration-200 hover:border-gray-400"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option>All statuses</option>
                  <option>Running</option>
                  <option>Completed</option>
                  <option>Submitted</option>
                  <option>Stopped</option>
                  <option>Failed</option>
                </select>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
              </div>

              {/* Date Filter */}
              <div className="w-[180px]">
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    value={dateFilter}
                    onChange={(newValue) => {
                      setDateFilter(newValue);
                    }}
                    format="MM/DD/YYYY"
                    slotProps={{
                      textField: {
                        size: "small",
                        className: "w-full border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary",
                        placeholder: "MM/DD/YYYY"
                      },
                      field: {
                        clearable: true,
                        onClear: () => {
                          setDateFilter(null);
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
              </div>

              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className={`flex items-center justify-center gap-1.5 px-3 py-2.5 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 whitespace-nowrap ${isRefreshing ? 'cursor-not-allowed opacity-50' : ''}`}
                aria-label="Refresh sessions"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                <span className="text-sm font-medium">Refresh</span>
              </button>

            </div>

            {/* Active Filters - Compact Display */}
            {(searchQuery || typeFilter !== 'All types' || statusFilter !== 'All statuses' || dateFilter) && (
              <div className="mt-3 flex flex-wrap gap-1.5">
                {searchQuery && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-primary-50 text-primary-700 border border-primary-200 rounded text-xs">
                    "{searchQuery}"
                    <button
                      onClick={() => setSearchQuery('')}
                      className="hover:bg-primary-100 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
                
                {typeFilter !== 'All types' && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-primary-50 text-primary-700 border border-primary-200 rounded text-xs">
                    {typeFilter}
                    <button
                      onClick={() => setTypeFilter('All types')}
                      className="hover:bg-primary-100 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
                
                {statusFilter !== 'All statuses' && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-50 text-green-700 border border-green-200 rounded text-xs">
                    {statusFilter}
                    <button
                      onClick={() => setStatusFilter('All statuses')}
                      className="hover:bg-green-100 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
                
                {dateFilter && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-50 text-purple-700 border border-purple-200 rounded text-xs">
                    {dayjs(dateFilter).format('MMM D, YYYY')}
                    <button
                      onClick={() => setDateFilter(null)}
                      className="hover:bg-purple-100 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-auto">
          {isLoading ? (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Session</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Duration</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <TableSkeleton />
              </tbody>
            </table>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  {renderTableHeader('type', 'Type')}
                  {renderTableHeader('title', 'Session')}
                  {renderTableHeader('status', 'Status')}
                  {renderTableHeader('date', 'Date')}
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Duration</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sessions.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="px-6 py-16 text-center">
                      <div className="flex flex-col items-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                          <Search className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-1">No sessions found</h3>
                        <p className="text-sm text-gray-500 max-w-md">
                          We couldn't find any sessions matching your current filters. Try adjusting your search criteria or create a new session.
                        </p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  sessions.map((session) => {
                    const isResumeDisabled = resumingSession === session.id || processedRequests.has(session.id);

                    return (
                      <tr key={session.id} className="hover:bg-gray-50 transition-colors duration-200">
                        {/* Type */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center gap-3">
                            <div className="p-2 bg-gray-50 rounded-lg">
                              {getIcon(session.icon)}
                            </div>
                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getTypeBadgeStyle(session.icon)}`}>
                              {getTypeLabel(session.icon)}
                            </span>
                          </div>
                        </td>

                        {/* Session Title */}
                        <td className="px-6 py-4 text-center">
                          <div className="flex flex-col items-center">
                            <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                              {session.title}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              ID: {session.id}
                            </div>
                          </div>
                        </td>

                        {/* Status */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusStyle(session.status)}`}>
                            {(session.status?.toLowerCase() === 'running' || session.status?.toLowerCase() === 'in_progress') && <div className="w-2 h-2 bg-primary rounded-full mr-1.5 animate-pulse"></div>}
                            {formatStatusDisplay(session.status)}
                          </span>
                        </td>

                        {/* Date */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="text-sm text-gray-900">
                            {session.date?.split('•')[0]?.trim()}
                          </div>
                          <div className="text-xs text-gray-500">
                            {session.date?.split('•')[1]?.trim()}
                          </div>
                        </td>

                        {/* Duration */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center text-sm text-gray-600">
                            <Clock size={14} className="mr-1.5" />
                            {session.duration}
                          </div>
                        </td>

                        {/* Actions */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center">
                            <button
                              onClick={() => handleResumeClick(session)}
                              disabled={isResumeDisabled}
                              className={`inline-flex items-center justify-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200 min-w-[120px] ${
                                isResumeDisabled
                                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                  : 'bg-primary-50 text-primary-700 hover:bg-primary-100 border border-primary-200'
                              }`}
                            >
                              {resumingSession === session.id ? (
                                <>
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span>Resuming...</span>
                                </>
                              ) : (
                                <>
                                  <Play size={14} />
                                  <span>Resume</span>
                                </>
                              )}
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sessions;