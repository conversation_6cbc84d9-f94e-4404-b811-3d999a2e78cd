import React, { useState, useEffect, useCallback, useRef } from "react";
import { 
  <PERSON>ader2, 
  <PERSON>fresh<PERSON><PERSON>,
  AlertTriangle,
  AlertCircle,
  Square
} from "lucide-react";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url) => {
  if (!url || typeof url !== 'string') return url;
  // Replace any occurrence of :3000 with :4000 in the URL
  return url.replace(':3000', ':4000');
};

const PreviewPanel = ({ currentTaskId }) => {
  const { 
    wsConnection, 
    containers, 
    selectedContainer,
    setSelectedContainer 
  } = useCodeGeneration();
  
  const [iframeKey, setIframeKey] = useState(Date.now());
  const [iframeError, setIframeError] = useState(false);
  const [errorType, setErrorType] = useState(null);
  const [isCheckingUrl, setIsCheckingUrl] = useState(false);
  const [urlStatus, setUrlStatus] = useState('checking');
  const iframeRef = useRef(null);

  // Auto-select first container if none is selected
  useEffect(() => {
    if (!selectedContainer && containers.length > 0) {
      setSelectedContainer(containers[0].name);
    }
  }, [selectedContainer, containers, setSelectedContainer]);

  // Find the selected container object
  const getSelectedContainerObj = useCallback(() => {
    return containers.find(c => c.name === selectedContainer);
  }, [containers, selectedContainer]);

  // Check URL accessibility before showing iframe
  const checkUrlAccessibility = useCallback(async (url) => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);

      if (response.status === 404) {  
        return { accessible: false, status: 404, error: 'Not Found' };
      } else if (response.status === 502) {
        return { accessible: false, status: 502, error: 'Bad Gateway' };
      } else if (response.ok) {
        return { accessible: true, status: response.status };
      } else {
        return { accessible: false, status: response.status, error: response.statusText };
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        return { accessible: false, status: 'timeout', error: 'Request timeout' };
      }
      
      // Handle CORS errors - if it's a CORS error, assume URL is accessible
      // since CORS errors occur when server responds but blocks cross-origin requests
      if (error.message.includes('CORS') || 
          error.message.includes('cross-origin') ||
          error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        return { accessible: true, status: 200, corsBlocked: true };
      }
      
      return { accessible: false, status: 'network', error: error.message };
    }
  }, []);

  // Restart container method
  const restartContainer = useCallback((containerName = selectedContainer) => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      wsConnection.send(JSON.stringify({
        type: "restart_container",
        task_id: currentTaskId,
        input_data: {
          container_name: containerName
        }
      }));
    }
  }, [wsConnection, currentTaskId, selectedContainer]);

  // Start container method (alias for restart)
  const startContainer = useCallback((containerName = selectedContainer) => {
    restartContainer(containerName);
  }, [restartContainer, selectedContainer]);

  // Check URL before loading iframe (no auto-retry)
  const handleUrlCheck = useCallback(async (url) => {
    if (!url) return;

    setIsCheckingUrl(true);
    setUrlStatus('checking');
    
    const result = await checkUrlAccessibility(url);
    
    if (result.accessible) {
      setUrlStatus('success');
      setIframeError(false);
      setErrorType(null);
    } else {
      setUrlStatus('error');
      setIframeError(true);
      
      if (result.status === 404) {
        setErrorType('404');
      } else if (result.status === 502) {
        setErrorType('502');
      } else {
        setErrorType('other');
      }
    }
    
    setIsCheckingUrl(false);
  }, [checkUrlAccessibility]);

  // Manual refresh function
  const refreshIframe = useCallback(() => {
    // Clear all states
    setIframeError(false);
    setErrorType(null);
    setIsCheckingUrl(false);
    setUrlStatus('checking');
    setIframeKey(Date.now());

    // Re-check URL
    const container = getSelectedContainerObj();
    if (container?.url) {
      setTimeout(() => {
        handleUrlCheck(processPreviewUrl(container.url));
      }, 100);
    }
  }, [handleUrlCheck, getSelectedContainerObj]);

  // Reset states when container changes
  useEffect(() => {
    setIframeError(false);
    setErrorType(null);
    setIsCheckingUrl(false);
    setUrlStatus('checking');
    setIframeKey(Date.now());
  }, [selectedContainer]);

  // Check URL when container becomes available
  useEffect(() => {
    const container = getSelectedContainerObj();
    if (container?.status === 'running' && container.url) {
      handleUrlCheck(processPreviewUrl(container.url));
    }
  }, [getSelectedContainerObj, handleUrlCheck]);

  const container = getSelectedContainerObj();

  // No containers available at all
  if (containers.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <p className="text-gray-500 text-sm">No containers available</p>
      </div>
    );
  }

  // Container not found
  if (!selectedContainer || !container) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <p className="text-gray-500 text-sm">Loading container...</p>
      </div>
    );
  }

  // Container is running - show iframe or error
  if (container.status === 'running' && container.url) {
    // Show checking message while determining URL accessibility
    if (isCheckingUrl || urlStatus === 'checking') {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-600 text-sm">
            Checking service availability...
          </p>
        </div>
      );
    }

    // Show error handling if URL is not accessible
    if (urlStatus === 'error' || iframeError) {
      const getErrorMessage = () => {
        switch (errorType) {
          case '404':
            return {
              icon: <AlertTriangle className="h-16 w-16 text-primary" />,
              title: "Project not available",
              description: "We couldn't find the project right now. It may be starting up — please check back shortly."
            };
          case '502':
            return {
              icon: <AlertTriangle className="h-16 w-16 text-primary" />,
              title: "Project is loading",
              description: "The project is taking longer than usual to respond. Please wait a moment and try again."
            };
          default:
            return {
              icon:<AlertTriangle className="h-16 w-16 text-primary" />,
              title: "Something went wrong",
              description: "We're having trouble connecting to the project. Please try again shortly."
            };
        }
      };

      const errorInfo = getErrorMessage();

      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          {errorInfo.icon}
          <p className="text-gray-600 text-sm font-medium text-center">
            {errorInfo.title}
          </p>
          <p className="text-gray-500 text-xs text-center max-w-md leading-relaxed">
            {errorInfo.description}
          </p>
          
          <button
            onClick={refreshIframe}
            className="px-6 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary-600 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </button>
        </div>
      );
    }

    // URL is accessible - show iframe
    if (urlStatus === 'success') {
      return (
        <div className="w-full h-full relative">
          <iframe
            ref={iframeRef}
            key={`${container.name}-${iframeKey}`}
            src={processPreviewUrl(container.url)}
            className="w-full h-full border-none"
            title={`Preview - ${container.name}`}
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
            allow="microphone; camera; midi; encrypted-media;"
            onError={() => {
              // If iframe fails to load after URL check passed, show error
              setIframeError(true);
              setErrorType('other');
            }}
          />
          
          {/* Floating refresh button */}
          <button
            onClick={refreshIframe}
            className="absolute top-4 right-4 p-2 bg-black/10 hover:bg-black/20 rounded-md transition-colors backdrop-blur-sm"
            title="Refresh preview"
          >
            <RefreshCw className="h-4 w-4 text-gray-700" />
          </button>
        </div>
      );
    }
  }

  // Container is building/starting
  if (container.status === 'building' || container.status === 'starting') {
    return (
      <div className="w-full h-full flex items-center justify-center flex-col gap-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-gray-600 text-sm">
          {container.name} is {container.status}...
        </p>
        <p className="text-gray-500 text-xs text-center max-w-md">
          Please wait while the container starts up. This may take a few moments.
        </p>
      </div>
    );
  }

  // Container is stopped/not started/failed
  return (
    <div className="w-full h-full flex items-center justify-center flex-col gap-4">
      {container.status === 'failed' || container.status === 'error' ? (
        <>
          <AlertCircle className="h-8 w-8 text-red-500" />
          <p className="text-gray-600 text-sm">
            {container.name} failed to start
          </p>
          {container.error && (
            <p className="text-gray-500 text-xs max-w-md text-center">
              {container.error}
            </p>
          )}
        </>
      ) : (
        <>
          <Square className="h-8 w-8 text-gray-400" />
          <p className="text-gray-600 text-sm">
            {container.name} is {container.status || 'not started'}
          </p>
        </>
      )}
      
    </div>
  );
};

export default PreviewPanel;